#include <iostream>
#include <queue>
#include <string>
#include <stack>

using namespace std;

class MedianStack {
private:
    // max heap for elements less than or equal to median
    priority_queue<int> left;
    // min heap for elements greater than median
    priority_queue<int, vector<int>, greater<int>> right;
    // actual stack to maintain order
    stack<int> st;
    
    // Balance the heaps to maintain median property
    void balance() {
        while (left.size() > right.size() + 1) {
            right.push(left.top());
            left.pop();
        }
        while (right.size() > left.size()) {
            left.push(right.top());
            right.pop();
        }
    }
    
    // Remove an element from the heaps
    void removeFromHeaps(int x) {
        if (!left.empty() && x <= left.top()) {
            // Create a temporary heap to remove the element
            priority_queue<int> temp;
            while (!left.empty()) {
                if (left.top() != x) {
                    temp.push(left.top());
                }
                left.pop();
            }
            left = temp;
        } else {
            // Create a temporary heap to remove the element
            priority_queue<int, vector<int>, greater<int>> temp;
            while (!right.empty()) {
                if (right.top() != x) {
                    temp.push(right.top());
                }
                right.pop();
            }
            right = temp;
        }
        balance();
    }

public:
    void push(int x) {
        st.push(x);
        if (left.empty() || x <= left.top()) {
            left.push(x);
        } else {
            right.push(x);
        }
        balance();
    }
    
    void pop() {
        if (st.empty()) return;
        
        int x = st.top();
        st.pop();
        removeFromHeaps(x);
    }
    
    int median() {
        return left.top();
    }
};

int main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    
    int n;
    cin >> n;
    
    MedianStack ms;
    
    while (n--) {
        string op;
        cin >> op;
        
        if (op == "push") {
            int x;
            cin >> x;
            ms.push(x);
        } else if (op == "pop") {
            ms.pop();
        } else if (op == "median") {
            cout << ms.median() << endl;
        }
    }
    
    return 0;
}s