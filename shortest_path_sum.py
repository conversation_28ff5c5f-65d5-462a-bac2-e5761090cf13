def floyd_warshall(graph, n, excluded_vertex=None):
    """
    使用Floyd-Warshall算法计算所有点对之间的最短距离
    excluded_vertex: 要排除的顶点编号（0-based）
    """
    # 创建距离矩阵的副本
    dist = [[float('inf')] * n for _ in range(n)]
    
    # 初始化距离矩阵
    for i in range(n):
        for j in range(n):
            if i == j:
                dist[i][j] = 0
            elif excluded_vertex is not None and (i == excluded_vertex or j == excluded_vertex):
                # 如果涉及被排除的顶点，设为无穷大
                dist[i][j] = float('inf')
            else:
                dist[i][j] = graph[i][j]
    
    # Floyd-Warshall算法
    for k in range(n):
        if excluded_vertex is not None and k == excluded_vertex:
            continue
        for i in range(n):
            if excluded_vertex is not None and i == excluded_vertex:
                continue
            for j in range(n):
                if excluded_vertex is not None and j == excluded_vertex:
                    continue
                if dist[i][k] + dist[k][j] < dist[i][j]:
                    dist[i][j] = dist[i][k] + dist[k][j]
    
    return dist

def calculate_sum_of_shortest_distances(graph, n, excluded_vertex=None):
    """
    计算剩余点之间所有最短距离的和
    """
    dist = floyd_warshall(graph, n, excluded_vertex)
    total_sum = 0
    
    for i in range(n):
        if excluded_vertex is not None and i == excluded_vertex:
            continue
        for j in range(i + 1, n):  # 只计算上三角，避免重复
            if excluded_vertex is not None and j == excluded_vertex:
                continue
            if dist[i][j] != float('inf'):
                total_sum += dist[i][j]
    
    return total_sum

def solve():
    # 读取输入
    n = int(input())
    graph = []
    for i in range(n):
        row = list(map(int, input().split()))
        graph.append(row)
    
    total_result = 0
    
    # 依次移除第 i 个点（1-based转换为0-based）
    for i in range(n):
        # 移除第 i+1 个点（在0-based中是第 i 个点）
        sum_distances = calculate_sum_of_shortest_distances(graph, n, excluded_vertex=i)
        total_result += sum_distances
        
        # 调试输出（可选）
        print(f"移除第 {i+1} 个点后，剩余点间最短距离之和: {sum_distances}")
    
    print(f"总和: {total_result}")
    return total_result

if __name__ == "__main__":
    solve()
