{"tasks": [{"type": "cppbuild", "label": "C/C++: gcc.exe 生成活动文件", "command": "D:\\gcc\\ucrt64\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "调试器生成的任务。"}, {"type": "cppbuild", "label": "C/C++: g++.exe 生成活动文件", "command": "D:/gcc/ucrt64/bin/g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "D:/gcc/ucrt64/bin"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "调试器生成的任务。"}], "version": "2.0.0"}