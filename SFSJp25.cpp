#include<iostream>
#include<algorithm>
using namespace std;

const int N = 1e5 + 10;

typedef pair<int,int> PII;

int n,W;
PII w[N];


int main(){

    scanf("%d%d",&n,&W);

    for(int i = 0;i<n;++i){
        int x;
        scanf("%d",&x);
        w[i] = {x,i};
    }

    sort(w,w+n);

    int idx = 0;
    printf("Target id of the bulk:");
    while(idx<n && W - w[idx].first >=0){
        printf("%d ",w[idx].second+1);
        W -= w[idx++].first;
    }

    system("pause");

    return 0;
}