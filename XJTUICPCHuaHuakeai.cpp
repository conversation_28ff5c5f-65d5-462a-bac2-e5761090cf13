#include<iostream>

using namespace std;

const int N = 100010;
typedef pair<int,int> PII;
int p[N];
int k;
char str[N];


int main(){
    
    scanf("%d%s",&k,str+1);
    int size = 0;
    int lgst = -1;
    for(int i = 1;str[i];++i){
        p[i] = str[i] - 'a';
        size++;
    }


    k = size - k;
    for(int t = 0;t<26 && k;++t){
        for(int i =size;~i;--i){
            if(p[i] == t && k){
                p[i] = -1;
                k--;
            }
        }
    }

    for(int i = 1;str[i];++i){
        if(p[i]>-1){
            printf("%c",str[i]);
        }
    }

    system("pause");

    return 0;
}