#include<iostream>
#include<set>
#include<queue>

using namespace std;

struct State{

    int a,b,c;
    State(int _a, int _b, int _c) : a(_a), b(_b), c(_c) {}

    bool operator<(const State& other) const {
        if (a != other.a) return a < other.a;
        if (b != other.b) return b < other.b;
        return c < other.c;
    }
};


queue<State> q;
set<int> Poc;
set<State> visited;

void pour(int &from, int &to, int toCapacity){
    int p = min(from, toCapacity - to);
    from -= p;
    to += p;
}

int main(){

    int A,B,C;
    while(cin >> A >> B >> C){
        visited.clear();
        Poc.clear();
        q.push(State(0, 0, C));
        visited.insert(State(0, 0, C));
        while(q.size()){
            State currState = q.front();
            Poc.insert(currState.c);
            q.pop();
            int a = currState.a , b = currState.b, c = currState.c;
            pour(a, b, B);
            if(visited.find(State(a, b, c)) == visited.end()){
                visited.insert(State(a, b, c));
                q.push(State(a, b, c));
            }

            a = currState.a , b = currState.b, c = currState.c;
            pour(b, a, A);
            if(visited.find(State(a, b, c)) == visited.end()){
                visited.insert(State(a, b, c));
                q.push(State(a, b, c));
            }

            a = currState.a , b = currState.b, c = currState.c;
            pour(a, c, C);
            if(visited.find(State(a, b, c)) == visited.end()){
                visited.insert(State(a, b, c));
                q.push(State(a, b, c));
            }

            a = currState.a , b = currState.b, c = currState.c;
            pour(c, a, A);
            if(visited.find(State(a, b, c)) == visited.end()){
                visited.insert(State(a, b, c));
                q.push(State(a, b, c));
            }

            a = currState.a , b = currState.b, c = currState.c;
            pour(b, c, C);
            if(visited.find(State(a, b, c)) == visited.end()){
                visited.insert(State(a, b, c));
                q.push(State(a, b, c));
            }

            a = currState.a , b = currState.b, c = currState.c;
            pour(c, b, B);
            if(visited.find(State(a, b, c)) == visited.end()){
                visited.insert(State(a, b, c));
                q.push(State(a, b, c));
            }

        }

        cout << Poc.size() << endl;
        for(const State &x: visited){
            cout << x.a << " " << x.b << " " << x.c << endl;
        }
    }

    return 0;
}