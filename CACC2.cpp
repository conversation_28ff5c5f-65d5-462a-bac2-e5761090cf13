#include<iostream>
#include<algorithm>
using namespace std;

typedef pair<int,int> PII;
const int N = 1e5 + 10;

PII q[N];

int n,m,k;


int main(){

  scanf("%d%d%d",&n,&m,&k);

  for(int i = 1;i<=n;++i){
    int t,c;

    scanf("%d%d",&t,&c);
    q[i] = {t,c};
  }

  sort(q+1,q+n+1);

    int i = n;
    int cst = 0;
    int count = 0;
    int val = q[i].first;
  while(i > 0 && q[i].first == val){
    cst += q[i].second;
    count++;
    i--;
  }
  while(m>=cst && val>=k+1){
    m -= cst;
    val--;
    while(i > 0 && q[i].first == val){
    cst += q[i].second;
    count++;
    i--;
  }
}

if(m>=cst){
  cout << k << endl;
}else{
  cout << val << endl;
}

  system("pause");
  return 0;
}