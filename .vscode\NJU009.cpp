#include<iostream>
#include<queue>
#include<cstring>
using namespace std;
const int N = 1e5 + 10;

int s,t;
queue<int> q;
bool st[N];
int dist[N], p[N], seq[N];

int bfs(int s, int t){
	
	if(s == t)return 0;
	q.push(s);
	while(q.size()){
		
		int u = q.front();
		if(u == t)return dist[t];
		q.pop();
		int ne[3] = {u + 1, u - 1, 2 * u};
		for(int i = 0;i<3;++i){
			int nextPos = ne[i];
			if(nextPos >= 0 && nextPos <= 1e5 && !st[nextPos]){
                st[nextPos] = true;
				dist[nextPos] = dist[u] + 1;
                p[nextPos] = u;
				q.push(nextPos);
			}
		}
	}
	return -1;
	
}
int main(){
	
	scanf("%d%d",&s,&t);
	cout << bfs(s,t) << endl;
    p[s] = -1;
    int curr = t;
    int idx = 0;
    while(~p[curr]){
        seq[idx++] = curr;
        curr = p[curr];
    }
    cout << s << "->";
    for(int i = idx - 1;i>=1;--i){
        cout << seq[i] << "->";
    }
    cout << t << endl;
	return 0;
}