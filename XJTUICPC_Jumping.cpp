// #include<iostream>

// using namespace std;

// const int N = 5010;
// typedef long long ll;
// int n;
// ll q[N];
// ll dp[N];


// int main(){
//     scanf("%d",&n);
//     for(int i = 1;i<=n;++i)cin >> q[i];
//     dp[0] = q[1] * n;
//     int l = 0;
//     for(int i = 0;i<n;++i){
//         if(-n*q[l+1]+q[l+1]*i+q[i+1]*n>0){
//             dp[i+1] = dp[i];
//         }else{
//             dp[i+1] = dp[i]-n*q[l+1]+q[l+1]*i+q[i+1]*n;
//             l = i;
//         }
//     }

//     cout << dp[n] << endl;

//     return 0;
// }

#include<iostream>

using namespace std;

const int N = 5010;
typedef long long ll;
int n;
ll q[N];
ll dp[N];


int main(){
    scanf("%d",&n);
    for(int i = 1;i<=n;++i){
        scanf("%lld",&q[i]);
        dp[i] = 1e18;
    }

    dp[0] = 0;
    dp[1] = q[1];
    for(int i = 0;i<n;++i){
        for(int j = i+1;j<=n;++j){
            dp[j] = min(dp[j],dp[i]+j*q[i+1]);
        }
    }

    cout << dp[n] << endl;
   

    return 0;
}