# 远程备份系统实现任务清单

## 1. 协议和消息格式实现
- [x] 创建message.h文件，定义命令消息(Cmd_Msg_T)和数据消息(Data_Msg_T)结构
- [x] 定义FILE_NAME_LEN为128和DATA_BUF_LEN为3000
- [x] 实现网络字节序转换函数(htons, htonl, ntohs, ntohl)

## 2. 客户端功能实现 (client.cc, client.h)
- [x] 实现"ls"命令功能 (25分)
  - [x] 发送CMD_LS命令消息到服务器
  - [x] 接收文件数量信息
  - [x] 接收并显示文件名列表
  - [x] 处理空文件夹情况
  - [x] 错误处理

- [x] 实现"send"命令功能 (10分)
  - [x] 解析用户输入的文件名
  - [x] 检查本地文件是否存在
  - [x] 发送CMD_SEND命令消息到服务器
  - [x] 处理文件已存在情况（询问是否覆盖）
  - [x] 建立TCP连接并发送文件数据
  - [x] 等待服务器确认
  - [x] 打印缓冲区大小和总段数
  - [x] 错误处理

- [x] 实现"remove"命令功能 (5分)
  - [x] 解析用户输入的文件名
  - [x] 发送CMD_REMOVE命令消息到服务器
  - [x] 接收服务器确认
  - [x] 错误处理

- [x] 实现"rename"命令功能 (5分)
  - [x] 解析用户输入的原文件名和新文件名
  - [x] 发送CMD_RENAME命令消息到服务器
  - [x] 接收服务器确认
  - [x] 错误处理

- [x] 实现"quit"命令功能 (5分)
  - [x] 退出客户端程序
  - [x] 无需与服务器通信

- [x] 实现"shutdown"命令功能 (5分)
  - [x] 发送CMD_SHUTDOWN命令消息到服务器
  - [x] 接收服务器确认
  - [x] 退出客户端程序
  - [x] 错误处理

## 3. 服务器功能实现 (server.cc, server.h)
- [x] 实现UDP套接字初始化和"Waiting"状态
  - [x] 创建UDP套接字
  - [x] 绑定到指定端口
  - [x] 打印端口号
  - [x] 等待客户端命令

- [x] 实现"ls"命令功能 (20分)
  - [x] 接收CMD_LS命令消息
  - [x] 检查备份文件夹
  - [x] 发送文件数量信息
  - [x] 发送文件名列表
  - [x] 处理空文件夹情况
  - [x] 错误处理

- [x] 实现"send"命令功能 (10分)
  - [x] 接收CMD_SEND命令消息
  - [x] 检查文件是否已存在
  - [x] 创建TCP套接字并发送端口号
  - [x] 接收文件数据并保存
  - [x] 发送确认消息
  - [x] 打印接收的总字节数和缓冲区大小
  - [x] 错误处理

- [x] 实现"remove"命令功能 (5分)
  - [x] 接收CMD_REMOVE命令消息
  - [x] 检查文件是否存在
  - [x] 删除文件
  - [x] 发送确认消息
  - [x] 错误处理

- [x] 实现"rename"命令功能 (5分)
  - [x] 接收CMD_RENAME命令消息
  - [x] 检查文件是否存在
  - [x] 重命名文件
  - [x] 发送确认消息
  - [x] 错误处理

- [x] 实现"shutdown"命令功能 (5分)
  - [x] 接收CMD_SHUTDOWN命令消息
  - [x] 发送确认消息
  - [x] 关闭服务器
  - [x] 错误处理

## 4. 错误处理
- [x] 实现套接字错误处理
- [x] 实现文件操作错误处理
- [x] 实现网络通信错误处理

## 5. 编译和测试
- [x] 创建Makefile
- [x] 测试所有命令功能
- [x] 测试文件传输完整性（使用二进制文件如PDF或图像）
- [x] 测试错误处理

## 6. 文档和提交
- [x] 创建README.txt文件
- [x] 准备最终提交文件
  - [ ] server.cc
  - [ ] server.h
  - [ ] client.cc
  - [ ] client.h
  - [ ] message.h
  - [ ] makefile
  - [ ] README.txt
