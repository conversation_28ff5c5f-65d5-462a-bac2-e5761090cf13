#include <iostream>
#include <queue>
#include <algorithm>
#include <cstring>
#include <string>
using namespace std;

const int N = 1e4 + 10;
char op[5];
int a, b, c, pos;
int idx, te;
struct Task{
    int id, p, t;
}task[N];

struct Compare {
    bool operator()(const Task &a, const Task &b) {
        return a.p > b.p; // 从小到大排序
    }
};
priority_queue<Task, vector<Task>, Compare> heap;
Task tmp[N];

int main() {
    int a, b;
    int h = 0;
    while (cin >> op >> a) { 


    	if(op[0] == 'a'){
            cin >> b >> c;
            heap.push({a, b, c});
        }else{
            te += a;
            while(te){
                if(heap.empty()){
                    break;
                }else{
                    auto task = heap.top();
                    if(task.t <= te){
                        te -= task.t;
                        heap.pop();
                    }else{
                        task.t -= te;
                        break;
                    }
                }
            }
        }
    }
    if(heap.empty()){
        cout << "idle" << endl;
    }else{
        cout << heap.top().id << endl;
    }
    return 0;
}

  