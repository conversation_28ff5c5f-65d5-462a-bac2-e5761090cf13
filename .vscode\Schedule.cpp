#include <iostream>

using namespace std;

const int N = 1e5 + 10;

int t1[N],t2[N];

int x[N],bestAnsX[N];

int bestans = 0x3f3f3f3f;  //int 变量的最大值

int n;


// f是当前的总用时
void dfs(int u,int f,int f1,int f2){

    if(u > n){

        if(f<bestans){
            bestans = f;

            for(int i = 1;i<=n;++i)bestAnsX[i] = x[i];
        }

        return;
    }

    for(int i = u;i<=n;++i){


        int f2_ = f2;
        
        swap(x[i],x[u]);
        
        
        f2 = max(f2,f1+t1[x[u]])+t2[x[u]];
        
        f += f2;
        
        if(f<bestans){
            
            dfs(u+1,f,f1+t1[x[u]],f2);
        }
        
        f -= f2;
        
        f2 = f2_;
        
        swap(x[i],x[u]);

    }
}

// x记录树的路径,也就是解空间
int main(int argv,char *argc[]){

    scanf("%d",&n);

    for(int i = 1;i<=n;++i){
        x[i] = i;
        scanf("%d%d",&t1[i],&t2[i]);
    }

    dfs(1,0,0,0);

    cout << bestans << endl;

    return 0;
}