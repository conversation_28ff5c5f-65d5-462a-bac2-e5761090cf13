#include<iostream>

using namespace std;

const int N = 1000010;
int q[N] = {0};  //创建队列

int hh,tt;
int a[N] = {0};
void init(){
    hh = 0;
    tt = -1;
}

int main(){
    int n = 0,k = 0;
    scanf("%d%d",&n,&k);
    for(int i = 0;i<n;i++)scanf("%d",&a[i]);

    init();
    for(int i = 0;i<n;i++){
        if(hh<=tt && i-k+1>q[hh])hh++;   //队首已经落后窗口，让队首指针前进
        while(hh<=tt && a[q[tt]]>a[i])tt--;  //保持队列的单调递增性
        q[++tt] = i;   //拉入新元素

        if(i>=k-1)printf("%d ",a[q[hh]]);  //输出队列中最小的元素
    }

    printf("\n");


    init();
    for(int i = 0;i<n;i++){
        if(hh<=tt && i-k+1>q[hh])hh++;   //队首已经落后窗口，让队首指针前进
        while(hh<=tt && a[q[tt]]<a[i])tt--;  //保持队列的单调递减性
        q[++tt] = i;   //拉入新元素

        if(i>=k-1)printf("%d ",a[q[hh]]);  //输出队列中最小的元素
    }

    return 0;

}