#include <cstdio>
#include <cstring>
#include <iostream>
using namespace std;
char s[3];
int main(){
    int n;
    scanf("%d",&n);
    int l = 0,r = n+1;
    while(l < r){
        int mid = (l+r) >>1;
        printf("%d\n",mid);
       // cout.flush();
        cin>>s;
        if(s[0]=='<') r = mid;
        else l = mid + 1;
    }
    printf("! %d\n",r-1);
    cout.flush();
    system("pause");
    return 0;
}