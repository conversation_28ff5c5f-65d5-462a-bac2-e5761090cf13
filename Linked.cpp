#include <iostream>
#include <cstring>


using namespace std;

const int N = 1e5 + 10;

int h[N],e[N],ne[N],idx = 0;

void add(int u,int v){
    e[idx] = v;
    ne[idx] = h[u];
    h[u] = idx++;
}

int main(){

    memset(h,-1,sizeof h);

    add(1,2),add(1,3),add(2,3);

    for(int i = 1;i <= 3;i++){
        cout << "node " << i << ": ";
        for(int j = h[i];j != -1;j = ne[j])
            cout << e[j] << " ";
        cout << endl;
    }

    return 0;

}