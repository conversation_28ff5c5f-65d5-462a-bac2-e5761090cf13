#include<iostream>
#include<algorithm>
using namespace std;

const int N = 1e5 + 10;

int q[N];

int UpperBound(int q[],int l,int r,int x){

    while(l<r){
        int mid = (l+r+1)>>1;
        if(q[mid]<=x){
            l = mid;
        }else{
            r = mid - 1;
        }
    }

    if(q[l]!=x)return -1;

    return l;
}

int LowerBound(int q[],int l,int r,int x){

    while(l<r){
        int mid = (l+r)>>1;
        if(q[mid]>=x){
            r = mid;
        }else{
            l = mid + 1;
        }
    }

    if(q[l]!=x)return -1;

    return l;
}

int main(){
    int n,m;
    scanf("%d%d",&n,&m);
    for(int i = 0;i<n;++i){
        scanf("%d",&q[i]);
    }

    sort(q,q+n);

    while(m--){
        int x;
        scanf("%d",&x);
        printf("%d %d\n",LowerBound(q,0,n-1,x),UpperBound(q,0,n-1,x));
    }
    system("pause");
    return 0;


}