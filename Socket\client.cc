#include "client.h"

// 初始化UDP套接字
int init_udp_socket() {
    int sockfd;
    
    // 创建UDP套接字
    if ((sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0) {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }
    
    return sockfd;
}

// 发送命令消息
void send_cmd_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Cmd_Msg_T cmd_msg) {
    // 网络字节序转换
    cmd_msg.size = htonl(cmd_msg.size);
    cmd_msg.port = htons(cmd_msg.port);
    cmd_msg.error = htons(cmd_msg.error);
    
    sendto(sockfd, &cmd_msg, sizeof(Cmd_Msg_T), 0, 
           (const struct sockaddr *)&addr, addr_len);
}

// 发送数据消息
void send_data_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Data_Msg_T data_msg) {
    sendto(sockfd, &data_msg, sizeof(Data_Msg_T), 0, 
           (const struct sockaddr *)&addr, addr_len);
}

// 处理ls命令
void handle_ls_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len) {
    Cmd_Msg_T cmd_msg, response;
    Data_Msg_T data_msg;
    int n, i;
    
    // 发送ls命令
    memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
    cmd_msg.command = CMD_LS;
    send_cmd_msg(sockfd, server_addr, addr_len, cmd_msg);
    
    // 接收服务器响应
    n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                 (struct sockaddr *)&server_addr, &addr_len);
    
    if (n < 0) {
        perror("recvfrom failed");
        return;
    }
    
    // 网络字节序转换
    response.size = ntohl(response.size);
    response.port = ntohs(response.port);
    response.error = ntohs(response.error);
    
    // 检查命令类型
    if (response.command != CMD_LS) {
        printf("- command response error\n");
        return;
    }
    
    // 处理空文件夹情况
    if (response.size == 0) {
        printf("- server backup folder is empty\n");
        return;
    }
    
    // 接收并显示文件名
    for (i = 0; i < response.size; i++) {
        memset(&data_msg, 0, sizeof(Data_Msg_T));
        n = recvfrom(sockfd, &data_msg, sizeof(Data_Msg_T), 0, 
                     (struct sockaddr *)&server_addr, &addr_len);
        
        if (n < 0) {
            perror("recvfrom failed");
            return;
        }
        
        printf("- %s\n", data_msg.data);
    }
}

// 处理send命令
void handle_send_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len, char* filename) {
    Cmd_Msg_T cmd_msg, response;
    FILE *file;
    int n, filesize;
    int tcp_sockfd;
    struct sockaddr_in tcp_server_addr;
    char buffer[DATA_BUF_LEN];
    int bytes_read;
    int total_segments = 0;
    char user_input[10];
    
    // 打开文件
    file = fopen(filename, "rb");
    if (!file) {
        printf("- cannot open file: %s\n", filename);
        return;
    }
    
    // 获取文件大小
    fseek(file, 0, SEEK_END);
    filesize = ftell(file);
    fseek(file, 0, SEEK_SET);
    
    printf("- filesize:%d\n", filesize);
    
    // 发送send命令
    memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
    cmd_msg.command = CMD_SEND;
    strncpy(cmd_msg.filename, filename, FILE_NAME_LEN);
    cmd_msg.size = filesize;
    cmd_msg.error = 0;
    send_cmd_msg(sockfd, server_addr, addr_len, cmd_msg);
    
    // 接收服务器响应
    n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                 (struct sockaddr *)&server_addr, &addr_len);
    
    if (n < 0) {
        perror("recvfrom failed");
        fclose(file);
        return;
    }
    
    // 网络字节序转换
    response.size = ntohl(response.size);
    response.port = ntohs(response.port);
    response.error = ntohs(response.error);
    
    // 检查文件是否已存在
    if (response.error == 2) {
        printf("file exists. overwrite? (y/n):");
        fgets(user_input, sizeof(user_input), stdin);
        
        if (user_input[0] == 'y' || user_input[0] == 'Y') {
            // 发送覆盖确认
            memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
            cmd_msg.command = CMD_SEND;
            strncpy(cmd_msg.filename, filename, FILE_NAME_LEN);
            cmd_msg.size = filesize;
            cmd_msg.error = 0;
            send_cmd_msg(sockfd, server_addr, addr_len, cmd_msg);
            
            // 接收服务器响应
            n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                         (struct sockaddr *)&server_addr, &addr_len);
            
            if (n < 0) {
                perror("recvfrom failed");
                fclose(file);
                return;
            }
            
            // 网络字节序转换
            response.size = ntohl(response.size);
            response.port = ntohs(response.port);
            response.error = ntohs(response.error);
        } else {
            printf("file transmission is cancelled\n");
            fclose(file);
            return;
        }
    }
    
    // 检查错误
    if (response.error == 1) {
        printf("- error or incorrect response from server\n");
        fclose(file);
        return;
    }
    
    // 检查是否收到TCP端口号
    if (response.port == 0) {
        printf("- error or incorrect response from server\n");
        fclose(file);
        return;
    }
    
    printf("- TCP port:%d\n", response.port);
    
    // 创建TCP套接字
    if ((tcp_sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        perror("TCP socket creation failed");
        fclose(file);
        return;
    }
    
    memset(&tcp_server_addr, 0, sizeof(tcp_server_addr));
    
    // 设置TCP服务器地址
    tcp_server_addr.sin_family = AF_INET;
    tcp_server_addr.sin_addr.s_addr = server_addr.sin_addr.s_addr;
    tcp_server_addr.sin_port = htons(response.port);
    
    // 连接到服务器
    if (connect(tcp_sockfd, (struct sockaddr *)&tcp_server_addr, sizeof(tcp_server_addr)) < 0) {
        perror("TCP connection failed");
        close(tcp_sockfd);
        fclose(file);
        return;
    }
    
    // 发送文件数据
    while ((bytes_read = fread(buffer, 1, DATA_BUF_LEN, file)) > 0) {
        if (send(tcp_sockfd, buffer, bytes_read, 0) < 0) {
            perror("TCP send failed");
            close(tcp_sockfd);
            fclose(file);
            return;
        }
        total_segments++;
    }
    
    printf("- buffer size: %d, total segments: %d\n", DATA_BUF_LEN, total_segments);
    
    // 关闭连接
    close(tcp_sockfd);
    fclose(file);
    
    // 接收确认消息
    n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                 (struct sockaddr *)&server_addr, &addr_len);
    
    if (n < 0) {
        perror("recvfrom failed");
        return;
    }
    
    // 网络字节序转换
    response.error = ntohs(response.error);
    
    // 检查确认
    if (response.command == CMD_ACK && response.error == 0) {
        printf("- file transmission is completed\n");
    } else {
        printf("- file transmission is failed\n");
    }
}

// 处理remove命令
void handle_remove_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len, char* filename) {
    Cmd_Msg_T cmd_msg, response;
    int n;
    
    // 发送remove命令
    memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
    cmd_msg.command = CMD_REMOVE;
    strncpy(cmd_msg.filename, filename, FILE_NAME_LEN);
    send_cmd_msg(sockfd, server_addr, addr_len, cmd_msg);
    
    // 接收服务器响应
    n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                 (struct sockaddr *)&server_addr, &addr_len);
    
    if (n < 0) {
        perror("recvfrom failed");
        return;
    }
    
    // 网络字节序转换
    response.error = ntohs(response.error);
    
    // 检查响应
    if (response.command == CMD_ACK) {
        if (response.error == 0) {
            printf("File removed successfully\n");
        } else if (response.error == 1) {
            printf("- file doesn't exist\n");
        }
    } else {
        printf("- command response error\n");
    }
}

// 处理rename命令
void handle_rename_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len, char* old_filename, char* new_filename) {
    Cmd_Msg_T cmd_msg, response;
    int n;
    char combined_filename[FILE_NAME_LEN];
    
    // 组合原始文件名和新文件名
    snprintf(combined_filename, FILE_NAME_LEN, "original_filename=%s, expected_filename=%s", old_filename, new_filename);
    
    // 发送rename命令
    memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
    cmd_msg.command = CMD_RENAME;
    strncpy(cmd_msg.filename, combined_filename, FILE_NAME_LEN);
    send_cmd_msg(sockfd, server_addr, addr_len, cmd_msg);
    
    // 接收服务器响应
    n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                 (struct sockaddr *)&server_addr, &addr_len);
    
    if (n < 0) {
        perror("recvfrom failed");
        return;
    }
    
    // 网络字节序转换
    response.error = ntohs(response.error);
    
    // 检查响应
    if (response.command == CMD_ACK) {
        if (response.error == 0) {
            printf("File renamed successfully\n");
        } else if (response.error == 1) {
            printf("- file doesn't exist\n");
        }
    } else {
        printf("- command response error\n");
    }
}

// 处理shutdown命令
void handle_shutdown_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len) {
    Cmd_Msg_T cmd_msg, response;
    int n;
    
    // 发送shutdown命令
    memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
    cmd_msg.command = CMD_SHUTDOWN;
    send_cmd_msg(sockfd, server_addr, addr_len, cmd_msg);
    
    // 接收服务器响应
    n = recvfrom(sockfd, &response, sizeof(Cmd_Msg_T), 0, 
                 (struct sockaddr *)&server_addr, &addr_len);
    
    if (n < 0) {
        perror("recvfrom failed");
        return;
    }
    
    // 网络字节序转换
    response.error = ntohs(response.error);
    
    // 检查响应
    if (response.command == CMD_ACK && response.error == 0) {
        printf("Server shutdown successfully\n");
    } else {
        printf("- command response error\n");
    }
}

int main(int argc, char *argv[]) {
    int sockfd;
    struct sockaddr_in server_addr;
    socklen_t addr_len = sizeof(server_addr);
    char command[256];
    char *token;
    char *server_ip = "127.0.0.1"; // 默认服务器IP
    int server_port = 0; // 默认服务器端口
    
    // 解析命令行参数
    if (argc > 1) {
        for (int i = 1; i < argc; i++) {
            if (strcmp(argv[i], "-address") == 0 && i + 1 < argc) {
                server_ip = argv[i + 1];
                i++;
            } else if (strcmp(argv[i], "-port") == 0 && i + 1 < argc) {
                server_port = atoi(argv[i + 1]);
                i++;
            }
        }
    }
    
    // 初始化UDP套接字
    sockfd = init_udp_socket();
    
    // 设置服务器地址
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(server_port);
    
    // 将IP地址转换为网络地址
    if (inet_pton(AF_INET, server_ip, &server_addr.sin_addr) <= 0) {
        perror("inet_pton failed");
        exit(EXIT_FAILURE);
    }
    
    printf("$ ");
    
    // 主循环
    while (fgets(command, sizeof(command), stdin) != NULL) {
        // 移除换行符
        command[strcspn(command, "\n")] = 0;
        
        // 解析命令
        token = strtok(command, " ");
        
        if (token == NULL) {
            printf("$ ");
            continue;
        }
        
        // 处理不同的命令
        if (strcmp(token, "ls") == 0) {
            handle_ls_command(sockfd, server_addr, addr_len);
        } else if (strcmp(token, "send") == 0) {
            token = strtok(NULL, " ");
            if (token != NULL) {
                handle_send_command(sockfd, server_addr, addr_len, token);
            } else {
                printf("- wrong command\n");
            }
        } else if (strcmp(token, "remove") == 0) {
            token = strtok(NULL, " ");
            if (token != NULL) {
                handle_remove_command(sockfd, server_addr, addr_len, token);
            } else {
                printf("- wrong command\n");
            }
        } else if (strcmp(token, "rename") == 0) {
            char *old_filename = strtok(NULL, " ");
            char *new_filename = strtok(NULL, " ");
            if (old_filename != NULL && new_filename != NULL) {
                handle_rename_command(sockfd, server_addr, addr_len, old_filename, new_filename);
            } else {
                printf("- wrong command\n");
            }
        } else if (strcmp(token, "shutdown") == 0) {
            handle_shutdown_command(sockfd, server_addr, addr_len);
        } else if (strcmp(token, "quit") == 0) {
            printf("Client quits\n");
            break;
        } else {
            printf("- wrong command\n");
        }
        
        printf("$ ");
    }
    
    close(sockfd);
    return 0;
}
