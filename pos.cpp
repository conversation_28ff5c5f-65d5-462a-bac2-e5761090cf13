// #include<iostream>

// using namespace std;

// const int N = 50;

// int st,ed;

// bool state[N];

// int ans = 1e9;
// void dfs(int u,int st,int ed,int step,int pos){
    
//     if(step*step>(ed-st)*(ed-st))return;
//     if(pos == ed && (step == -1 || step == 1)){
//         ans = min(ans,u);
//         return;
//     }
    
//     for(int i = 1;i<=3;++i){
//         if(!state[i]){
//             state[i] = true;
//             if(i == 1){
//                 step += 1;
//                 dfs(u+1,st,ed,step,pos+step);
//                 step -= 1;
//             }else if(i == 2){
//                 step -= 1;
//                 dfs(u+1,st,ed,step,pos+step);
//                 step += 1;
//             }else{
//                 dfs(u+1,st,ed,step,pos);
//             }
//         }
//         state[i] = false;
//     }
//  }

// int main(){

//     scanf("%d%d",&st,&ed);

//     dfs(0,st,ed,0,st);


//     cout << ans << endl;
//     system("pause");
//     return 0;

// }



#include <iostream>
#include <vector>
#include <climits>

using namespace std;

const int N = 50;
const int INF = 1e9;

int st, ed;
vector<vector<int>> dp(N * N, vector<int>(N * 2, -1));

int dfs(int u, int step, int pos) {
    if (pos == ed && (step == -1 || step == 1)) {
        return u;
    }
    if (u >= INF || pos < 0 || pos > N * 2) {
        return INF;
    }
    if (dp[u][pos] != -1) {
        return dp[u][pos];
    }

    int res = INF;
    for (int i = 1; i <= 3; ++i) {
        int newStep = step;
        if (i == 1) {
            newStep += 1;
        } else if (i == 2) {
            newStep -= 1;
        }
        res = min(res, dfs(u + 1, newStep, pos + newStep));
    }

    dp[u][pos] = res;
    return res;
}

int main() {
    ios_base::sync_with_stdio(false);
    cin.tie(nullptr);

    cin >> st >> ed;

    int ans = dfs(0, 0, st);

    cout << (ans == INF ? -1 : ans) << endl;
    system("pause");
    return 0;
}