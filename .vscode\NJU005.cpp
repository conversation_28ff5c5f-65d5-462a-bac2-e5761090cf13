#include <iostream>
#include <string>
#include <cstring>

const int N = 1010, MOD = 1e9 + 7;

char s[N], t[N];
int Q;
using namespace std;

int dp[N][N];
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);
    
    cin >> Q;
    while(Q --){
        cin >> s >> t;
        int n = strlen(s), m = strlen(t);
        for(int i = 0;i<=n;++i){
            dp[i][0] = 1;
        }

        for(int i = 1;i<=n;++i){
            for(int j = 1;j<=m;++j){
                if(s[i - 1] == t[j - 1]){
                    dp[i][j] = dp[i - 1][j - 1] + dp[i - 1][j];
                }else{
                    dp[i][j] = dp[i - 1][j];
                }
            }
        }

        cout << ((dp[n][m] + MOD) % MOD) << endl;
    }

    return 0;
}
