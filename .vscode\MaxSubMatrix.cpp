#include<iostream>

using namespace std;

const int N = 1e3 + 10;

int g[N][N], sg[N][N];
int arr[N];
int n,m,ans = -1e9;

int findMax(){
    int price = 0;
    int ans = arr[0];
    for(int i = 1;i<=m;++i){
        if(price < 0){
            price = 0;
        }
        price += arr[i];
        ans = max(ans, price);
    }

    return ans;
}

int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    cin >> n >> m;

    for(int i = 1;i<=n;++i){
        for(int j = 1;j<=m;++j){
            cin >> g[i][j];
        }
    }

    for(int j = 1;j<=m;++j){
        for(int i = 1;i<=n;++i){
            sg[i][j] = sg[i-1][j] + g[i][j];
        }
    }

    for(int t = 1;t<=n;++t){
        for(int b=t;b<=n;++b){
            for(int k = 1;k<=m;++k){
                arr[k] = sg[b][k] - sg[t-1][k];
            }
            ans = max(ans, findMax());                    
        }
    }

    cout << ans << endl;
    return 0;
}


/*
5 5
7 5 1 -7 -4
2 -5 6 11 -3
2 4 -8 -6 -3
2 1 0 7 -5
2 5 -9 --4 -3

*/