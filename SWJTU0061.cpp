#include<iostream>
#include<queue>
#include<string.h>
#include<math.h>
using namespace std;

const int N = 1000010;
char str[2*N];
queue<char> q;
int p = 131;
unsigned long long h[N];

int main(){
    int n,m;

    scanf("%d%s%d",&n,str+1,&m);

    strncpy(str+1+n,str+1,n);

    int r = 1;

    for(int l = 1;str[r];++r){
        while(r-l<n){
            h[l] = h[l] * p + str[r];
            ++r;
        }

        h[++l] = h[l]*p + str[r] - str[l] * pow(p,n);
    }


    while(m--){
        char tes[N];
        char rev[N];
        scanf("%s",tes+1);
        for(int i = n,j = 0;i>0;--i){
            rev[++j] = tes[i];
        }

        int r = 1;
        unsigned long long  a = 0,b = 0;
        for(int l = 1;tes[r];++r){
            while(r-l<n){
                a = a * p + tes[r];
                b = b * p + rev[r];
                ++r;
            }
        }



        for(int i = 1;i<=n;++i){
            if(h[i] == a || h[i] == b){
                puts("YES");
                break;
            }

            if(i == n){
                puts("NO");
            }
        }




    }

    system("pause");

    return 0;
}