#include<iostream>
#include<cstring>
#include<vector>

const int N = 1 << 12, M = 12;
using namespace std;

typedef long long ll;

ll f[N][M];
bool st[N];

int n, m;
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    while(cin >> n >> m, n || m){
        memset(f, 0, sizeof f);
        for(int i = 0;i< 1<< n; ++i){
            st[i] = true;
            int cnt = 0;
            for(int j = 0;j<n;++j){
                if((i >> j) & 1){
                    if(cnt % 2){
                        st[i] = false;
                        cnt = 0;
                    }
                }else{
                    cnt ++ ;
                }
        }
        if(cnt % 2){
            st[i] = false;
        }
    }

        f[0][0] = 1;
        for(int j = 1; j <= m;++j){
            for(int i = 0;i < 1 << n;++i){
                for(int k = 0;k < 1 << n;++k){
                    if((i & k) == 0 && st[i | k]){
                        f[i][j] += f[k][j - 1];
                    }
                }
            }
        }

        cout << f[0][m] << endl;
    }

    return 0;
}
