#include<iostream>
using namespace std;
int ary[35];
int t;
int n = 1;
void f(int top) {
    int d = ary[top];
    if (top == 0) {
        for (int i = 1; i <= d / 2; i++) {
            ary[0] = i;
            ary[1] = d - i;
            f(top + 1);
            ary[0] = d;
        }
    }
    else {
        for (int i = ary[top - 1]; i <= d / 2; i++) {
            ary[top] = i;
            ary[top + 1] = d - i;
            f(top + 1);
            ary[top] = d;
        }
    }
    cout << t << "=";
    if (top == 0) {
        cout << t;
        if(n % 4 == 0)cout << endl;
    }
    else {
        for (int i = 0; i <= top; i++) {
            if (i == top) {
                cout << ary[i] << ";";
            }
            else {
                cout << ary[i] << "+";
            }
        }
        if (n % 4 == 0) {
            cout << endl;
        }
        
    }
    
    n++;

}
int main() {
    cin >> t;
    ary[0] = t;
    f(0);
    system("pause");
    return 0;
}


