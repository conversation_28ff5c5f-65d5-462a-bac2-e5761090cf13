#include <iostream>
#include<cstring>
#include<string>
using namespace std;
// we have defined the necessary header files here for this problem.
// If additional header files are needed in your program, please import here.
const int N = 1e5 + 10;
string s;
int length;
int q[N];
int k;
int myq[N];
int hh = 0, tt = -1;
int main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    getline(cin, s);
    for(int i = 0, j = 0;i<s.size();i = i + 2){
        q[j++] = s[i] - '0';
        length = j;
    }
    
    cin >> k;
    if(k<=0 || k > length){
        cout << -1 << endl;
        return 0;
    }
    for(int i = 1;i<length;++i){
        int x = 110;;
        int ans = i - k;
        for(int j = i-k;j<i+k;++j){
            if(j<0)continue;
            if(q[ans] < x){
                x = q[ans];
                ans = j;
            }
        }
        cout << ans << " ";
    }
    
    // please define the C++ input here. For example: int a,b; cin>>a>>b;;
    // please finish the function body here.
    // please define the C++ output here. For example:cout<<____<<endl;

    return 0;
}
