# 远程备份系统 - README.txt

## 项目概述
这是一个基于UDP/TCP套接字编程的远程备份系统，允许客户端与服务器之间进行文件备份和管理操作。系统实现了六个命令功能：ls、send、remove、rename、quit和shutdown。

## 功能说明
1. **ls**: 列出服务器备份文件夹中的所有文件
2. **send**: 将本地文件发送到服务器的备份文件夹
3. **remove**: 从服务器的备份文件夹中删除指定文件
4. **rename**: 重命名服务器备份文件夹中的文件
5. **quit**: 退出客户端程序
6. **shutdown**: 关闭服务器

## 实现细节
- 使用UDP套接字进行命令传输
- 使用TCP套接字进行文件数据传输
- 实现了网络字节序转换，确保不同计算机架构之间的通信正常
- 处理各种错误情况，如文件不存在、文件已存在等

## 文件结构
- **message.h**: 定义命令消息(Cmd_Msg_T)和数据消息(Data_Msg_T)结构
- **client.h**: 客户端头文件，定义客户端函数
- **client.cc**: 客户端源代码，实现客户端功能
- **server.h**: 服务器头文件，定义服务器函数
- **server.cc**: 服务器源代码，实现服务器功能
- **makefile**: 编译脚本

## 编译方法
使用make命令编译项目：
```
make
```

这将生成两个可执行文件：server和client

## 使用方法
1. 启动服务器：
```
./server [-port 端口号]
```
如果不指定端口号，系统将自动分配一个端口。

2. 启动客户端：
```
./client [-address 服务器IP] [-port 服务器端口]
```
默认服务器IP为127.0.0.1。

3. 在客户端输入命令：
- `ls`: 列出服务器备份文件夹中的所有文件
- `send 文件名`: 发送本地文件到服务器
- `remove 文件名`: 从服务器删除指定文件
- `rename 原文件名 新文件名`: 重命名服务器上的文件
- `quit`: 退出客户端程序
- `shutdown`: 关闭服务器

## 注意事项
- 服务器会在当前目录下创建一个名为"backup"的文件夹用于存储备份文件
- 客户端发送文件时，文件必须存在于当前目录
- 如果发送的文件在服务器上已存在，客户端会询问是否覆盖

## 已实现功能
- 客户端"ls"命令功能
- 客户端"send"命令功能
- 客户端"remove"命令功能
- 客户端"rename"命令功能
- 客户端"quit"命令功能
- 客户端"shutdown"命令功能
- 服务器"ls"命令功能
- 服务器"send"命令功能
- 服务器"remove"命令功能
- 服务器"rename"命令功能
- 服务器"shutdown"命令功能
