#include<iostream>

using namespace std;

const int N = 50010;

int p[N],d[N];

int cnt = 0;
int n,m;

int find(int x){
    if(p[x] == x)return x;

    int t = find(p[x]);
    d[x] += d[p[x]];
    p[x] = t;

    return p[x];
}


int main(){
    scanf("%d%d",&n,&m);
    for(int i = 1;i<=n;++i)p[i] = i;

    while(m--){
        int D,x,y;
        scanf("%d%d%d",&D,&x,&y);

        if(x>n || y>n){
            cnt++;
            continue;
        }

        int px = find(x),py = find(y);

        if(D == 1){

            if(px == py && (d[x]-d[y])%3)cnt++;

            if(px != py){
                p[py] = px;
                d[py] = d[x] - d[y];
            }    
        }else{
            if(px == py && (d[x]-d[y]-1)%3)cnt++;

            if(px != py){
                p[py] = px;
                d[py] = d[x] - d[y] -1;
            }
        }
    }   

    cout << cnt << endl;
    system("pause");
    return 0;
}