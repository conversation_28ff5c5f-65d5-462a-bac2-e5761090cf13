#include<iostream>
#include<cstring>

using namespace std;

const int N = 15;

int n,m;

int s = 0;

int g[N][N];
int maxS(int l,int i,int j){
    if(l == 1){
        s = max(g[i][j],s);
        return g[i][j];
    }

    int st = 1;
    for(int i = 1;i+l-1<=n;++i){
        for(int j = 1;j+l-1<=m;++j){
            st = st & maxS(l-1,i,j);
            st = st & maxS(l-1,i+l-1,j);
            st = st & maxS(l-1,i,j+l-1);
            st = st & maxS(l-1,i+l-1,j+l-1);
            if(st){
                s = max(s,l*l);
            }
        }
    }
    return st;
}

int main(){

    scanf("%d%d",&n,&m);

    for(int i = 1;i<=n;++i){

        for(int j = 1;j<=m;++j){
            scanf("%d",&g[i][j]);
        }
    }


    maxS(min(n,m),1,1);

    printf("%d\n",s);

    system("pause");
    return 0;

}