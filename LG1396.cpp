#include<iostream>
#include<cstring>
#include<algorithm>

using namespace std;

const int N = 1e4 + 10,M = 2e4 + 10;

int res;

int p[N];

struct Edges{
    int u,v,w;
    
    bool operator < (const Edges &W)const{
        return w<W.w;
    }
}edges[M];

int n,m,s,t;

int find(int x){
    if(x == p[x])return x;
    
    p[x] = find(p[x]);
    
    return p[x];
}

int Kruskal(){
    
    for(int i =1;i<=n;++i)p[i] = i;
    
    sort(edges+1,edges+1+m);
    
    for(int i =1;i<=m;++i){
        auto x = edges[i];
        
        int u = x.u,v = x.v,w = x.w;
        
        int pu = find(u),pv = find(v);
        
        if(pu != pv){
            p[pv] = pu;
            if(find(s) == find(t))return w;
        }
    }
    
    return 0;
}
int main(){
    
    scanf("%d%d%d%d",&n,&m,&s,&t);
    
    for(int i = 1;i<=m;++i){
        int x,y,z;
        scanf("%d%d%d",&x,&y,&z);
        edges[i] = {x,y,z};
    }
    
    cout << Kruskal() << endl;
    system("pause");
    return 0;
    
}