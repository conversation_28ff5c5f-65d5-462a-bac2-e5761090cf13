#include<iostream>
#include<cstring>
#include<queue>

using namespace std;

const int N = 510, M = 2e5 + 10;

int h[N], e[M], ne[M], w[M];

int dist[N];

bool st[N];
int idx, n, m, cnt, ans, u = 1;

void add(int u,int v,int wi){
    e[idx] = v;
    ne[idx] = h[u];
    w[idx] = wi;
    h[u] = idx ++;
}

int main(){
    
    scanf("%d%d",&n,&m);
    
    memset(dist, 0x3f, sizeof dist);
    memset(h, -1, sizeof h);
    while(m--){
        int u,v,wi;
        scanf("%d%d%d",&u,&v,&wi);
        add(u,v,wi);
        add(v,u,wi);
    }
    dist[1] = 0;
    for(int i = 1;i<=n;++i){
        int t = -1;
        for(int j = 1;j<=n;++j){
            if(!st[j]){
                if(t == -1 || dist[j] < dist[t]){
                    t = j;
                }
            }
        }
        st[t] = true;
        if(dist[t] == 0x3f3f3f3f){
            cout << "impossible" << endl;
            return 0;
        }else{
            ans += dist[t];
        }
        for(int j = h[t];~j;j=ne[j]){
            int v = e[j];
            if(!st[v]){
                dist[v] = min(dist[v], w[j]);
            }
        }
    }
    
    
    cout << ans << endl;
    
    return 0;
    
}
