#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[N];
int t, n, p, cnt;

int main(){

    scanf("%d",&n);

    for(int i = 0;i<n;++i){
        scanf("%d",&q[i]);
        if(q[i])cnt ++;
    }

    for(int i = 0;i<cnt;++i){
        if(q[i])p ++;
    }
    
    for(int i = 1;i + cnt - 1 < n;++i){
        int j = i + cnt - 1;
        if(q[i-1])p -= 1;
        if(i < j){
            if(q[j])p += 1;
        }
        t = max(t, p);
    }

    cout << cnt - t << endl;

    return 0;
}