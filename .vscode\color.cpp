#include <iostream>
#include <cstring>
#include <vector>
using namespace std;

const int N = 50;

int h[N],ne[N],e[N],idx = 0;

int color[N];

char C[N][20] = {"Index","Red","Blue","<PERSON>","Yellow","Purple"};

int n,m,k;

int x[N];

bool exsit;

void add(int u,int v){

 e[idx] = v;
 ne[idx] = h[u];
 h[u] = idx++;
}

void dfs(int u){

 if(u>n){
     for(int i = 1;i<=n;++i){
          printf("Color of %d : %s\n",i,C[color[i]]);
     }
     exsit = true;
     return;
 }

  for(int i = 1;i<=k;++i){
      color[u] = i;

      bool flag = true;
      for(int j = h[u];~j;j=ne[j]){
          int v = e[j];

          if(color[v] == color[u])flag = false;
      }

      if(flag && !exsit){
          dfs(u+1);
      }
  }

}
int main(int argc, char* argv[]){

 memset(h,-1,sizeof h);
 // n为节点的数量，m为无向边的数量
 scanf("%d%d%d",&n,&m,&k);

 while(m--){
     int a,b;

     scanf("%d%d",&a,&b);

     // a->b有一条边，b->a也有一条边
     add(a,b);
     add(b,a);
 }

 dfs(1);

 if(!exsit)cout << "No Solution" << endl;

    system("pause");
    return 0;

}
