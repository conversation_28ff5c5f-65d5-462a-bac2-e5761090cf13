#include <iostream>
#include <cstring>
#include <string>

using namespace std;

const int N = 220;

int idx;
char e[N];
int l[N], r[N];
string s;

int build(){
    if(idx >= s.size() || s[idx] == '#'){
        e[idx] = '#';
        l[idx] = r[idx] = -1;
        return idx++;
    }

    int u = idx;
    e[u] = s[idx++];
    l[u] = build();
    r[u] = build();

    return u;
}

void inorder(int u){
    if(e[u] == '#')return;

    inorder(l[u]);
    cout << e[u] << " ";
    inorder(r[u]);
}
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    while(getline(cin, s)){
        idx = 0;
        int rt = build();
        inorder(rt);
        cout << endl;
    }

    return 0;
}