#include "server.h"

// 备份文件夹路径
#define BACKUP_FOLDER "./backup"

// 初始化UDP套接字
int init_udp_socket(int port) {
    int sockfd;
    struct sockaddr_in server_addr;
    
    // 创建UDP套接字
    if ((sockfd = socket(AF_INET, SOCK_DGRAM, 0)) < 0) {
        perror("socket creation failed");
        exit(EXIT_FAILURE);
    }
    
    memset(&server_addr, 0, sizeof(server_addr));
    
    // 设置服务器地址
    server_addr.sin_family = AF_INET;
    server_addr.sin_addr.s_addr = INADDR_ANY;
    server_addr.sin_port = htons(port);
    
    // 绑定套接字到指定端口
    if (bind(sockfd, (const struct sockaddr *)&server_addr, sizeof(server_addr)) < 0) {
        perror("bind failed");
        exit(EXIT_FAILURE);
    }
    
    // 创建备份文件夹（如果不存在）
    mkdir(BACKUP_FOLDER, 0777);
    
    return sockfd;
}

// 发送命令消息
void send_cmd_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Cmd_Msg_T cmd_msg) {
    // 网络字节序转换
    cmd_msg.size = htonl(cmd_msg.size);
    cmd_msg.port = htons(cmd_msg.port);
    cmd_msg.error = htons(cmd_msg.error);
    
    sendto(sockfd, &cmd_msg, sizeof(Cmd_Msg_T), 0, 
           (const struct sockaddr *)&addr, addr_len);
}

// 发送数据消息
void send_data_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Data_Msg_T data_msg) {
    sendto(sockfd, &data_msg, sizeof(Data_Msg_T), 0, 
           (const struct sockaddr *)&addr, addr_len);
}

// 处理ls命令
void handle_ls_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len) {
    DIR *dir;
    struct dirent *entry;
    Cmd_Msg_T response;
    Data_Msg_T data_msg;
    int file_count = 0;
    
    printf("[CMD RECEIVED]: ls\n");
    
    // 打开备份文件夹
    dir = opendir(BACKUP_FOLDER);
    if (!dir) {
        printf("- server backup folder is empty\n");
        
        // 发送空文件夹响应
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_LS;
        response.size = 0;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    // 计算文件数量
    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_type == DT_REG) { // 只计算常规文件
            file_count++;
        }
    }
    
    // 如果文件夹为空
    if (file_count == 0) {
        printf("- server backup folder is empty\n");
        
        // 发送空文件夹响应
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_LS;
        response.size = 0;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        closedir(dir);
        return;
    }
    
    // 发送文件数量
    memset(&response, 0, sizeof(Cmd_Msg_T));
    response.command = CMD_LS;
    response.size = file_count;
    send_cmd_msg(sockfd, client_addr, addr_len, response);
    
    // 重新打开目录以遍历文件
    rewinddir(dir);
    
    // 发送每个文件名
    while ((entry = readdir(dir)) != NULL) {
        if (entry->d_type == DT_REG) { // 只发送常规文件
            memset(&data_msg, 0, sizeof(Data_Msg_T));
            strncpy(data_msg.data, entry->d_name, FILE_NAME_LEN);
            send_data_msg(sockfd, client_addr, addr_len, data_msg);
            printf("- %s\n", entry->d_name);
        }
    }
    
    closedir(dir);
}

// 处理send命令
void handle_send_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len, Cmd_Msg_T cmd_msg) {
    Cmd_Msg_T response;
    char filepath[FILE_NAME_LEN + 20]; // 文件路径 = 备份文件夹路径 + 文件名
    FILE *file;
    int tcp_sockfd, new_sockfd;
    struct sockaddr_in tcp_server_addr, tcp_client_addr;
    socklen_t tcp_addr_len = sizeof(tcp_client_addr);
    char buffer[DATA_BUF_LEN];
    int bytes_received = 0;
    int total_bytes = 0;
    
    // 网络字节序转换
    cmd_msg.size = ntohl(cmd_msg.size);
    cmd_msg.port = ntohs(cmd_msg.port);
    cmd_msg.error = ntohs(cmd_msg.error);
    
    printf("[CMD RECEIVED]: send %s\n", cmd_msg.filename);
    
    // 构建文件路径
    snprintf(filepath, sizeof(filepath), "%s/%s", BACKUP_FOLDER, cmd_msg.filename);
    
    // 检查文件是否已存在
    file = fopen(filepath, "r");
    if (file) {
        fclose(file);
        printf("file %s exist; overwrite?\n", cmd_msg.filename);
        
        // 发送错误消息，表示文件已存在
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 2;
        strcpy(response.filename, cmd_msg.filename);
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    // 创建TCP套接字
    if ((tcp_sockfd = socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        perror("TCP socket creation failed");
        
        // 发送错误消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    memset(&tcp_server_addr, 0, sizeof(tcp_server_addr));
    
    // 设置TCP服务器地址
    tcp_server_addr.sin_family = AF_INET;
    tcp_server_addr.sin_addr.s_addr = INADDR_ANY;
    tcp_server_addr.sin_port = 0; // 让系统自动分配端口
    
    // 绑定TCP套接字
    if (bind(tcp_sockfd, (struct sockaddr *)&tcp_server_addr, sizeof(tcp_server_addr)) < 0) {
        perror("TCP bind failed");
        close(tcp_sockfd);
        
        // 发送错误消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    // 获取分配的端口号
    socklen_t len = sizeof(tcp_server_addr);
    if (getsockname(tcp_sockfd, (struct sockaddr *)&tcp_server_addr, &len) < 0) {
        perror("getsockname failed");
        close(tcp_sockfd);
        
        // 发送错误消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    // 监听连接请求
    if (listen(tcp_sockfd, 5) < 0) {
        perror("listen failed");
        close(tcp_sockfd);
        
        // 发送错误消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    printf("- listen @:%d\n", ntohs(tcp_server_addr.sin_port));
    
    // 发送TCP端口号给客户端
    memset(&response, 0, sizeof(Cmd_Msg_T));
    response.command = CMD_SEND;
    response.error = 0;
    response.port = ntohs(tcp_server_addr.sin_port);
    send_cmd_msg(sockfd, client_addr, addr_len, response);
    
    // 接受客户端连接
    if ((new_sockfd = accept(tcp_sockfd, (struct sockaddr *)&tcp_client_addr, &tcp_addr_len)) < 0) {
        perror("accept failed");
        close(tcp_sockfd);
        
        // 发送错误消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    printf("- connected with the client\n");
    
    // 打开文件准备写入
    file = fopen(filepath, "wb");
    if (!file) {
        perror("file open failed");
        close(new_sockfd);
        close(tcp_sockfd);
        
        // 发送错误消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_SEND;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
        return;
    }
    
    printf("- filename:%s - filesize:%d\n", cmd_msg.filename, cmd_msg.size);
    
    // 接收文件数据
    while ((bytes_received = recv(new_sockfd, buffer, DATA_BUF_LEN, 0)) > 0) {
        fwrite(buffer, 1, bytes_received, file);
        total_bytes += bytes_received;
    }
    
    fclose(file);
    close(new_sockfd);
    close(tcp_sockfd);
    
    printf("- total bytes received: %d, buffer size: %d\n", total_bytes, DATA_BUF_LEN);
    printf("- send acknowledgement\n");
    
    // 发送确认消息
    memset(&response, 0, sizeof(Cmd_Msg_T));
    response.command = CMD_ACK;
    response.error = 0;
    send_cmd_msg(sockfd, client_addr, addr_len, response);
}

// 处理remove命令
void handle_remove_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len, Cmd_Msg_T cmd_msg) {
    Cmd_Msg_T response;
    char filepath[FILE_NAME_LEN + 20]; // 文件路径 = 备份文件夹路径 + 文件名
    
    printf("[CMD RECEIVED]: remove %s\n", cmd_msg.filename);
    
    // 构建文件路径
    snprintf(filepath, sizeof(filepath), "%s/%s", BACKUP_FOLDER, cmd_msg.filename);
    
    // 尝试删除文件
    if (remove(filepath) != 0) {
        printf("- file doesn't exist\n");
        
        // 发送错误消息，表示文件不存在
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_ACK;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
    } else {
        // 发送成功消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_ACK;
        response.error = 0;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
    }
}

// 处理rename命令
void handle_rename_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len, Cmd_Msg_T cmd_msg) {
    Cmd_Msg_T response;
    char old_filepath[FILE_NAME_LEN + 20]; // 旧文件路径
    char new_filepath[FILE_NAME_LEN + 20]; // 新文件路径
    char old_filename[FILE_NAME_LEN];
    char new_filename[FILE_NAME_LEN];
    
    // 解析原始文件名和新文件名
    // 格式: "original_filename=oldname, expected_filename=newname"
    sscanf(cmd_msg.filename, "original_filename=%[^,], expected_filename=%s", old_filename, new_filename);
    
    printf("[CMD RECEIVED]: rename %s to %s\n", old_filename, new_filename);
    
    // 构建文件路径
    snprintf(old_filepath, sizeof(old_filepath), "%s/%s", BACKUP_FOLDER, old_filename);
    snprintf(new_filepath, sizeof(new_filepath), "%s/%s", BACKUP_FOLDER, new_filename);
    
    // 尝试重命名文件
    if (rename(old_filepath, new_filepath) != 0) {
        printf("- file doesn't exist\n");
        
        // 发送错误消息，表示文件不存在
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_ACK;
        response.error = 1;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
    } else {
        // 发送成功消息
        memset(&response, 0, sizeof(Cmd_Msg_T));
        response.command = CMD_ACK;
        response.error = 0;
        send_cmd_msg(sockfd, client_addr, addr_len, response);
    }
}

// 处理shutdown命令
void handle_shutdown_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len) {
    Cmd_Msg_T response;
    
    printf("[CMD RECEIVED]: shutdown\n");
    
    // 发送确认消息
    memset(&response, 0, sizeof(Cmd_Msg_T));
    response.command = CMD_ACK;
    response.error = 0;
    send_cmd_msg(sockfd, client_addr, addr_len, response);
    
    printf("Server shuts down\n");
    
    // 关闭套接字并退出
    close(sockfd);
    exit(0);
}

int main(int argc, char *argv[]) {
    int sockfd;
    struct sockaddr_in client_addr;
    Cmd_Msg_T cmd_msg;
    socklen_t addr_len = sizeof(client_addr);
    int port = 0; // 默认让系统选择端口
    
    // 如果提供了端口参数，则使用指定的端口
    if (argc > 1) {
        if (strcmp(argv[1], "-port") == 0 && argc > 2) {
            port = atoi(argv[2]);
        }
    }
    
    // 初始化UDP套接字
    sockfd = init_udp_socket(port);
    
    // 获取实际使用的端口号
    struct sockaddr_in server_addr;
    socklen_t len = sizeof(server_addr);
    if (getsockname(sockfd, (struct sockaddr *)&server_addr, &len) < 0) {
        perror("getsockname failed");
        exit(EXIT_FAILURE);
    }
    
    printf("Waiting UDP command @: %d\n", ntohs(server_addr.sin_port));
    
    // 主循环
    while (1) {
        memset(&cmd_msg, 0, sizeof(Cmd_Msg_T));
        
        // 接收命令消息
        if (recvfrom(sockfd, &cmd_msg, sizeof(Cmd_Msg_T), 0, 
                    (struct sockaddr *)&client_addr, &addr_len) < 0) {
            perror("recvfrom failed");
            continue;
        }
        
        // 根据命令类型处理请求
        switch (cmd_msg.command) {
            case CMD_LS:
                handle_ls_command(sockfd, client_addr, addr_len);
                break;
            case CMD_SEND:
                handle_send_command(sockfd, client_addr, addr_len, cmd_msg);
                break;
            case CMD_REMOVE:
                handle_remove_command(sockfd, client_addr, addr_len, cmd_msg);
                break;
            case CMD_RENAME:
                handle_rename_command(sockfd, client_addr, addr_len, cmd_msg);
                break;
            case CMD_SHUTDOWN:
                handle_shutdown_command(sockfd, client_addr, addr_len);
                break;
            default:
                printf("Unknown command\n");
                break;
        }
        
        printf("Waiting UDP command @: %d\n", ntohs(server_addr.sin_port));
    }
    
    return 0;
}
