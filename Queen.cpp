#include<iostream>

using namespace std;

const int N = 20;

bool col[N],dg[N],udg[N];

int n,sum;

char g[N][N];


void dfs(int u){
    if(u == n){
        for(int i = 0;i<n;++i){
            puts(g[i]);
        }
        sum++;
        puts("");
        return;
    }

    for(int i = 0;i<n;++i){
        if(!col[i] && !dg[u+i] && !udg[n+i-u]){
            col[i] = dg[u+i] = udg[n+i-u] = true;
            g[u][i] = 'Q';
            dfs(u+1);
            col[i] = dg[u+i] = udg[n+i-u] = false;
            g[u][i] = '.';
        }
    }
}

int main(){

    scanf("%d",&n);

    for(int i = 0;i<n;++i){
        for(int j = 0;j<n;++j){
            g[i][j] = '.';
        }
    }

    dfs(0);

    printf("sum = %d\n",sum);
    system("pause");

    return 0;
}