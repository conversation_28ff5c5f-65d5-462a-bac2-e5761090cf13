#include<iostream>
#include<cstring>
using namespace std;

const int N = 1010;
int f[12][12];
char a[N][12];
char b[N];
int n,m;

int dp(char a[], char b[], int l1,int l2){
    for(int i = 0;i<=l1;++i)f[i][0] = i;
    for(int j = 0;j<=l2;++j)f[0][j] = j;

    for(int i = 1;i<=l1;++i){
        for(int j = 1;j<=l2;++j){
            f[i][j] = min(f[i - 1][j], f[i][j - 1]) + 1;
            if(a[i] == b[j]){
                f[i][j] = min(f[i][j], f[i - 1][j - 1]);
            }else{
                f[i][j] = min(f[i][j], f[i - 1][j - 1] + 1);
            }
        }
    }

    return f[l1][l2];
}
int main(){

    scanf("%d%d",&n,&m);
    for(int i = 1;i<=n;++i){
        scanf("%s",a[i] + 1);
    }

    while(m--){
        int limit;
        int ans = 0;
        scanf("%s%d",b + 1, &limit);
        for(int i = 1;i<=n;++i){
            if(dp(a[i] , b, strlen(a[i] + 1), strlen(b + 1)) <= limit){
                ans ++;
            }
        }
        cout << ans << endl;
    }

    return 0;
}