快速排序

```C++

#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[N];

void qsort(int q[],int l,int r){
    
    if(l>=r)return;
    
    int x = q[(l+r)>>1];
    
    int i = l - 1;
    int j = r + 1;
    while(i<j){
        while(q[++i]<x);
        while(q[--j]>x);
        
        if(i<j){
            swap(q[i],q[j]);
        }
    }
    
    qsort(q,l,j);
    qsort(q,j+1,r);
}
int main(){
    
    int n;
    scanf("%d",&n);
    
    for(int i = 0;i<n;++i)scanf("%d",&q[i]);
    
    qsort(q,0,n-1);
    
    for(int i = 0;i<n;++i)printf("%d ",q[i]);
    
    return 0;
}

```

第K个数
```C++

#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int n,k;

int q[N];

void swap(int i,int j){
    int temp = q[i];
    q[i] = q[j];
    q[j] = temp;
}

int qsort(int l,int r,int k){

    if(l == r)return q[l];

    int i = l - 1;
    int j = r + 1;
    int x = q[(l+r)>>1];

    while(i<j){
        while(q[++i]<x);
        while(q[--j]>x);
        if(i<j){
            swap(i,j);
        }
    }

    int sl = j - l + 1;
    if(k<=sl)return qsort(l,j,k);

    return qsort(j+1,r,k-sl);
}

int main(){
    scanf("%d%d",&n,&k);

    for(int i = 0;i<n;++i)scanf("%d",&q[i]);

    cout << qsort(0,n-1,k) << endl;

    return 0;
}

归并排序
```C++

#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[N];
int temp[N];

void merge(int q[],int l,int r){
    if(l>=r)return;
    
    int mid = (l+r)>>1;
    
    merge(q,l,mid);
    merge(q,mid+1,r);
    
    
    int i = l;
    int j = mid + 1;
    int k = 0;
    
    while(i<=mid && j<=r){
        if(q[i]<q[j]){
            temp[k++] = q[i++];
        }else{
            temp[k++] = q[j++];
        }
    }
    
    while(i<=mid)temp[k++] = q[i++];
    while(j<=r)temp[k++] = q[j++];
    
    for(int k = 0,i = l;i<=r;++i){
        q[i] = temp[k++]; 
    }
}

int main(){
    
    int n;
    scanf("%d",&n);
    for(int i = 0;i<n;++i)scanf("%d",&q[i]);
    
    merge(q,0,n-1);
    
    for(int i = 0;i<n;++i)printf("%d ",q[i]);
    
    return 0;
    
}

    ```

逆序对数量
```C++

#include<iostream>

using namespace std;

const int N = 100010;
int temp[N] = {0};
long long merge(int q[],int l,int r){
    if(l>=r)return 0;
    
    int mid = (l+r)>>1;
    
    long long res = merge(q,l,mid) + merge(q,mid+1,r);
    
    int i = l;
    int j = mid+1;
    int k = 0;
    
    while(i<=mid && j<=r){
        if(q[i]<=q[j]){
            temp[k++] = q[i++];
    }else{
        temp[k++] = q[j++];
        res += mid - i + 1;
       }
    }
    
    while(i<=mid)temp[k++] = q[i++];
    while(j<=r) temp[k++] = q[j++];
    
    for(int i = l,k=0;i<=r;i++){
        q[i] = temp[k++];
    }
    
    return res;
    
}

int main(){
    int n = 0;
    scanf("%d",&n);
    int q[N] = {0};
    for(int i=0;i<n;i++)scanf("%d",&q[i]);
    
    printf("%ld",merge(q,0,n-1));
    return 0;
}
```

整数二分

```C++
#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[N];

int UpperBound(int q[],int l,int r,int x){

    while(l<r){
        int mid = (l+r+1)>>1;
        if(q[mid]<=x){
            l = mid;
        }else{
            r = mid - 1;
        }
    }

    if(q[l]!=x)return -1;

    return l;
}

int LowerBound(int q[],int l,int r,int x){

    while(l<r){
        int mid = (l+r)>>1;
        if(q[mid]>=x){
            r = mid;
        }else{
            l = mid + 1;
        }
    }

    if(q[l]!=x)return -1;

    return l;
}

int main(){
    int n,m;
    scanf("%d%d",&n,&m);
    for(int i = 0;i<n;++i){
        scanf("%d",&q[i]);
    }

    while(m--){
        int x;
        scanf("%d",&x);
        printf("%d %d\n",LowerBound(q,0,n-1,x),UpperBound(q,0,n-1,x));
    }

    return 0;


}
```

三次开方
```C++
#include<iostream>

using namespace std;

int main(){
    double l = -1000;
    double r = 10000;
    double n = 0;
    scanf("%lf",&n);
    double mid = 0;
    while(r-l>1e-8){
        mid = (l+r)/2;
        if(mid*mid*mid<=n){
            l = mid;
        }else{
            r = mid;
        }
    }
    
    printf("%lf",mid);
    return 0;
}
```

最长不连续子序列
```C++

#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[N],h[N];


bool check(int i,int j){
    if(h[q[i]]>1){
        h[q[j]]--; // 因为后续要j++，因此h[q[j]]的数量-1
        return true;
    }
    
    return false;
}
int main(){
    
    int n,len;
    scanf("%d",&n);
    
    for(int i = 0;i<n;++i)scanf("%d",&q[i]);
    
    for(int i = 0,j = 0;i<n;++i){
        h[q[i]]++;
        
        while(j<=i && check(i,j))j++;
        
        len = max(len,i-j+1);
    }
    
    cout << len << endl;
    
    return 0;
}
```