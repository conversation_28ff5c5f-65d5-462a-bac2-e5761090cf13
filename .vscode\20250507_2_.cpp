#include <iostream>
#include <cstring>

using namespace std;

const int N = 1010;

int h[N], e[N], ne[N], q[N];
int f[N][2];
int idx, n;

void add(int a,int b){
    e[idx] = b, ne[idx] = h[a], h[a] = idx++;
}

void dfs(int u){
    if(q[u] == -1){
        return;
    }
    if(h[u] == -1){
        f[u][1] = 1;
        return;
    }
    for(int i = h[u]; ~i; i = ne[i]){
        int j = e[i];
        dfs(j);
        f[u][0] +=  f[j][1];
        f[u][1] += min(f[j][0], f[j][1]); // 如果选择了当前节点u，则子节点j不能选择
    }
    f[u][1] += 1;
}
int main(){

    scanf("%d",&n);
    memset(f, 0x3f, sizeof f); // 初始化f数组为无穷大，因为我们需要找到最小值，所以初始化为无穷大是合理的选择。
    memset(h, -1, sizeof h);
    memset(q, -1, sizeof q); // 初始化q数组为-1，表示没有被访问过
    for(int i = 0;i<n;++i){
        char c;
        cin >> c;
        if(c == 'N'){
            q[i] = -1;
        }else{
            q[i] = c - '0';
        }
    }

    for(int i = 0;i<n;++i){
        if(q[2 * i + 1] != -1){
            add(i, 2 * i + 1);
        }

        if(q[2 * i + 2]!= -1){
            add(i, 2 * i + 2);
        }
    }

    dfs(0);

    cout << f[0][1] << endl;

    return 0;
}