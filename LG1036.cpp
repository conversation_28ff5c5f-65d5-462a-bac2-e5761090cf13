#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[N];
bool st[N];
int res = 0;
int n,k;
bool prim(int x){
    for(int i = 2;i<=x/2;++i){
        if(x%i==0)return false;
    }    
    
    return true;
}


void dfs(int u,int x,int curr){
    if(u == k && prim(x)){
        res++;
        return;
    }

    if(u == k)return;
    
    for(int i = 1;i<=n;++i){
        if(!st[i] && i>curr){
            st[i] = true;
            int temp = curr;
            curr = i;
            dfs(u+1,x+q[i],curr);
            st[i] = false;
            curr = temp;
        }
    } 
    
}
int main(){
    

    scanf("%d%d",&n,&k);
    for(int i = 1;i<=n;++i)scanf("%d",&q[i]);
    
    dfs(0,0,0);
    printf("%d",res);
    
    return 0;
}