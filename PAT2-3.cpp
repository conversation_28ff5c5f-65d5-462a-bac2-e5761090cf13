#include<iostream>
#include<cstring>
#include<queue>
#include<vector>

using namespace std;

typedef pair<int,int> PII;
const int N = 510,M = 510 * 510;


bool state[N];

int e[M],ne[M],wgt[M],idx = 0;

int h[N],q[N],dist[N];

int n,m,st,ed,sum;

struct CustomCompare {
    bool operator()(const PII & lhs, const PII & rhs) const {
        // 自定义比较规则，例如比较第一个元素，如果相同再比较第二个元素
        if (lhs.first == rhs.first) {
            return q[lhs.second] > q[rhs.second];  // 第一个元素相同时，比较第二个元素
        }
        return lhs.first < rhs.first;  // 否则比较第一个元素
    }
};



priority_queue<PII,vector<PII>,CustomCompare>heap;


void insert(int u,int v,int w){
    e[idx] = v;
    ne[idx] = h[u];
    wgt[idx] = w;
    h[u] = idx++;
}

int dij(){

    dist[st] = 0;
    heap.push({0,st});
    
    while(heap.size()){
        auto t = heap.top();
        
        heap.pop();

        int l = t.first,u = t.second;
        state[u] = true;
        
        sum = sum + q[u];

        if(u == ed)return dist[ed];

        for(int i = h[u];~i;i=ne[i]){
            int j = e[i];
            if(!state[j]){
                if(dist[j]>dist[u]+wgt[i]){
                    dist[j] = dist[u] + wgt[i];
                    heap.push({dist[j],j});
                }
            }
            
        }
    }

    return 0x3f3f3f3f;
}
int main(){

    memset(h,-1,sizeof h);
    memset(dist,0x3f3f3f3f,sizeof dist);

    scanf("%d%d%d%d",&n,&m,&st,&ed);

    for(int i = 0;i<n;++i)scanf("%d",&q[i]);

    while(m--){
        int x,y,z;
        scanf("%d%d%d",&x,&y,&z);
        insert(x,y,z);
        insert(y,x,z);
    }

    printf("%d ",dij());
    printf("%d\n",sum);

    return 0;
    
}