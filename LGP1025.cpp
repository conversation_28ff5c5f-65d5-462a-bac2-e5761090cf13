#include<iostream>

using namespace std;

const int N = 210;

int cnt = 0;

int n,k;


void dfs(int u,int v,int w){
    if(u == k-1){
        if(w>=v)cnt++;

        return;
    }

    for(int i = 1;i<=n;++i){
        if(v<=i){
            dfs(u+1,i,w-i);
        }
    }
}

int main(){
    
    scanf("%d%d",&n,&k);

    dfs(0,0,n);
    
    cout << cnt << endl;
    
    system("pause");
    return 0;
}