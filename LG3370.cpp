#include<iostream>
#include<algorithm>
using namespace std;

const int N = 1510,M = 10010;;

typedef unsigned long long ll;

int pp = 131;

ll ans[M];

int res = 1;

char str[N];

ll Hash(char str[]){

    ll hh = 0;
    for(int i=1;str[i];++i){
        hh = hh * pp + str[i];
    }

    return hh;
}

int main(){
    int n;
    scanf("%d",&n);
    
    for(int i = 0;i<n;++i){
        scanf("%s",str+1);
        ans[i] = Hash(str+1);
    }

    sort(ans,ans+n);

    for(int i = 0;i<n-1;++i){
        if(ans[i]!=ans[i+1])res++;
    }

    cout << res << endl;

    system("pause");
    return 0;

}