#include<iostream>

using namespace std;

int a,b,c,d,e,f;

int A,C,M;
int main(){

    scanf("%d%d%d%d%d%d",&a,&b,&c,&d,&e,&f);
    
    A = c * d* e* (f*(f - 1) / 2+ f* b);
    C = c * d *e * (f * (f - 1)/2 + a * f + b * f + a * b);
    M = c * d *(f* (f-1)*(f-2)*(f >= 2) / 6 + f
*b*(b-1) / 2 + f*(f-1)*a*(f>=1) / 2 + a * b*(b-1) / 2 + a * b* f + (f * f - f) / 2 * b);

    cout << A << " " << C << " " << M << endl;

    cout << C + M << endl;
    return 0;
}