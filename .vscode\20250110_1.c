#include<iostream>
#include <string>

using namespace std;

const int N = 1e5+10;


int q[N];


void build(){

    string input;

    getline(cin, input);

    int i = 0;

    for (char c : input){

        if(isdigit(c) && q[++i] == 0){

            q[++i] = c - '0';
        }else{

            q[++i] = -1;

            q[2 * i + 1] = -1;

            q[2 * i + 2] = -1;
        }
    }

}
int main(){


    char val[8];


    while(scanf("%s",val[1])){
        if(val[1] == 'None'){

            q[i]
        }
    }
}