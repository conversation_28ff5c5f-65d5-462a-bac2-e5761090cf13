// 该程序用于解决棋子从 N×M 方格棋盘左上角出发，不重复经过方格并遍历整个棋盘后回到起点的问题
// 只有当棋盘的行数 N 和列数 M 都为偶数时，才存在这样的路径
#include <iostream>
#include <vector>

// 检查 (x, y) 是否在棋盘内且未被访问过
bool isValid(int x, int y, int N, int M, const std::vector<std::vector<bool>>& visited) {
    return x >= 0 && x < N && y >= 0 && y < M && !visited[x][y];
}

// 回溯函数，尝试找到哈密尔顿回路
bool findHamiltonianCycle(int x, int y, int step, int N, int M, std::vector<std::vector<bool>>& visited, std::vector<std::pair<int, int>>& path) {
    visited[x][y] = true;
    path[step] = {x, y};

    if (step == N * M - 1) {
        // 检查是否能回到起点
        if ((x == 0 && y == 1) || (x == 1 && y == 0)) {
            return true;
        }
    } else {
        // 定义四个方向：右、下、左、上
        int dx[] = {0, 1, 0, -1};
        int dy[] = {1, 0, -1, 0};

        for (int i = 0; i < 4; ++i) {
            int newX = x + dx[i];
            int newY = y + dy[i];
            if (isValid(newX, newY, N, M, visited) && findHamiltonianCycle(newX, newY, step + 1, N, M, visited, path)) {
                return true;
            }
        }
    }

    visited[x][y] = false;
    return false;
}

int main() {
    int N, M;
    std::cout << "请输入棋盘的行数 N 和列数 M：";
    std::cin >> N >> M;

    if (N % 2 != 0 || M % 2 != 0) {
        std::cout << "只有当 N 和 M 都为偶数时，才存在这样的路径。" << std::endl;
        return 1;
    }

    std::vector<std::vector<bool>> visited(N, std::vector<bool>(M, false));
    std::vector<std::pair<int, int>> path(N * M);

    if (findHamiltonianCycle(0, 0, 0, N, M, visited, path)) {
        std::cout << "找到路径：" << std::endl;
        for (const auto& p : path) {
            std::cout << "(" << p.first << ", " << p.second << ") ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "未找到路径。" << std::endl;
    }

    return 0;
}