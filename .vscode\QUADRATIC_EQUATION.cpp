#include<iostream>
#include<cstring>
#include<stack>
#include<cmath>
using namespace std;

char s[50];

bool r;
bool negative;
stack<int> st;
bool isdigit(char c){
    return '0' <= c and c <= '9';
}

int Tonumber(int st, int ed){
    int val = 0;
    if(st > ed)return 1;
    for(int i = st;i <= ed;++i){
        val += s[i] - '0';
        s[i] = '#';
        val = val * 10;
    }
    
    return val / 10;
}
int main(){

    
    scanf("%s", s);
    
    int n = strlen(s);

    int a = 0, b = 0, cst = 0;

    for(int i = 0;i<n;++i){
        char c = s[i];
        
        if(c == '='){
            r = true;
        }
        
        
        if(c == 'x'){
            if(i < n - 1 && s[i + 1] == '^'){
                int j = i - 1;
                while(j > -1 && isdigit(s[j]))j = j - 1;
                if(j > -1 and s[j] == '-'){
                    negative = true;
                    s[j] = '#';
                }
                int p = Tonumber(j + 1, i - 1);
                p = negative ? -p : p;
                negative = false;
                a = r ? a - p : a + p;
                s[i + 1] = '#';
                s[i + 2] = '#';
            }else{
                
                int j = i - 1;
                while(j > -1 && isdigit(s[j]))j = j - 1;
                if(j > -1 and s[j] == '-'){
                    negative = true;
                    s[j] = '#';
                }
                int p = Tonumber(j + 1, i - 1);
                p = negative ? -p : p;
                negative = false;
                b = r ? b - p : b + p;
                
                
            }
            
            s[i] = '#';
        }
    }
    
    
    r = false;
    negative = false;
    int i = 0;
    while(i < n){
        if(s[i] == '=')r = true;
        
        if(isdigit(s[i])){
            if(i > 0 && s[i - 1] == '-')negative = true;
            int j = i;
            while(j < n && isdigit(s[j]))j = j + 1;
            int p = Tonumber(i, j - 1);
            if(not r){
                if(not negative){
                    cst = cst + p;
                }else{
                    cst = cst - p;
                }
            }else{
                if(not negative){
                    cst = cst - p;
                }else{
                    cst = cst + p;
                }
            }
            
            negative = false;
            i = j;
        }
        i = i + 1;
    }
    
    double u = a, v = b, w = cst;
    if(a == 0 && b == 0){
        if(cst != 0){
            cout << "No Solution" << endl;
        }
    }else if(a == 0){
        printf("%.2lf", -w / v);
    }else{
        
        if(b * b - 4 * a * cst == 0){
            double ans1 = -v / (2 * u);
            printf("%.2lf %.2lf", ans1, ans1);
        }else if(b * b - 4 * a * cst > 0){
            double delta = sqrt(v * v - 4 * u * w);
            double ans1 = (-v - delta) / (2 * u), ans2 = (-v + delta) / (2 * u);
            printf("%.2lf %.2lf", ans1, ans2);
        }else{
            cout << "No Solution" << endl;
        }
    }
    return 0;
    
}