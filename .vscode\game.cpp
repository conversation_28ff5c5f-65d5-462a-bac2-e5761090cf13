#include <iostream>
#include <windows.h>
#include <conio.h>
#include <vector>
#include <ctime>
#include <cstdlib>

using namespace std;

// 游戏区域大小（调整为更合适的比例）
const int WIDTH = 30;  // 减小宽度
const int HEIGHT = 20;

// 游戏字符（使用ASCII字符）
const char WALL = '+';    // 使用+号作为墙壁
const char SNAKE = 'O';   // 使用O作为蛇身
const char FOOD = '@';    // 使用@作为食物
const char EMPTY = ' ';   // 空格保持不变

// 方向枚举
enum Direction {
    STOP = 0,
    LEFT,
    RIGHT,
    UP,
    DOWN
};

// 坐标结构体
struct Point {
    int x, y;
    Point(int _x = 0, int _y = 0) : x(_x), y(_y) {}
    bool operator==(const Point& p) const {
        return x == p.x && y == p.y;
    }
};

class SnakeGame {
private:
    bool gameOver;
    Point food;
    vector<Point> snake;
    Direction dir;
    int score;
    HANDLE console;
    COORD cursorPosition;

    // 初始化游戏
    void init() {
        gameOver = false;
        dir = STOP;
        score = 0;
        
        // 初始化蛇的位置（从中间开始）
        snake.clear();
        snake.push_back(Point(WIDTH/2, HEIGHT/2));
        
        // 生成第一个食物
        generateFood();
        
        // 隐藏光标
        CONSOLE_CURSOR_INFO cursorInfo;
        GetConsoleCursorInfo(console, &cursorInfo);
        cursorInfo.bVisible = false;
        SetConsoleCursorInfo(console, &cursorInfo);
    }

    // 生成食物
    void generateFood() {
        srand(time(0));
        while (true) {
            food.x = rand() % (WIDTH - 2) + 1;
            food.y = rand() % (HEIGHT - 2) + 1;
            
            // 确保食物不会生成在蛇身上
            bool onSnake = false;
            for (const auto& p : snake) {
                if (p == food) {
                    onSnake = true;
                    break;
                }
            }
            if (!onSnake) break;
        }
    }

    // 绘制游戏界面
    void draw() {
        // 移动光标到开始位置
        cursorPosition.X = 0;
        cursorPosition.Y = 0;
        SetConsoleCursorPosition(console, cursorPosition);

        // 绘制上边界
        for (int i = 0; i < WIDTH; i++)
            cout << WALL;
        cout << endl;

        // 绘制游戏区域
        for (int i = 0; i < HEIGHT-2; i++) {
            for (int j = 0; j < WIDTH; j++) {
                if (j == 0 || j == WIDTH-1)
                    cout << WALL;
                else {
                    bool printed = false;
                    // 绘制蛇
                    for (const auto& p : snake) {
                        if (p.x == j && p.y == i+1) {
                            cout << SNAKE;
                            printed = true;
                            break;
                        }
                    }
                    // 绘制食物
                    if (!printed && food.x == j && food.y == i+1) {
                        cout << FOOD;
                        printed = true;
                    }
                    // 绘制空白
                    if (!printed)
                        cout << EMPTY;
                }
            }
            cout << endl;
        }

        // 绘制下边界
        for (int i = 0; i < WIDTH; i++)
            cout << WALL;
        cout << endl;

        // 显示分数
        cout << "Score: " << score << endl;
    }

    // 处理输入
    void input() {
        if (_kbhit()) {
            switch (_getch()) {
                case 'a':
                    if (dir != RIGHT) dir = LEFT;
                    break;
                case 'd':
                    if (dir != LEFT) dir = RIGHT;
                    break;
                case 'w':
                    if (dir != DOWN) dir = UP;
                    break;
                case 's':
                    if (dir != UP) dir = DOWN;
                    break;
                case 'x':
                    gameOver = true;
                    break;
            }
        }
    }

    // 更新游戏状态
    void update() {
        if (gameOver) return;

        // 保存蛇头的当前位置
        Point newHead = snake.front();

        // 根据方向移动蛇头
        switch (dir) {
            case LEFT:
                newHead.x--;
                break;
            case RIGHT:
                newHead.x++;
                break;
            case UP:
                newHead.y--;
                break;
            case DOWN:
                newHead.y++;
                break;
            default:
                return;
        }

        // 检查是否撞墙
        if (newHead.x <= 0 || newHead.x >= WIDTH-1 ||
            newHead.y <= 0 || newHead.y >= HEIGHT-1) {
            gameOver = true;
            return;
        }

        // 检查是否撞到自己
        for (const auto& p : snake) {
            if (newHead == p) {
                gameOver = true;
                return;
            }
        }

        // 移动蛇
        snake.insert(snake.begin(), newHead);

        // 检查是否吃到食物
        if (newHead == food) {
            score += 10;
            generateFood();
        } else {
            snake.pop_back();
        }
    }

public:
    SnakeGame() {
        console = GetStdHandle(STD_OUTPUT_HANDLE);
        init();
    }

    void run() {
        while (!gameOver) {
            draw();
            input();
            update();
            Sleep(100); // 控制游戏速度
        }

        // 游戏结束显示
        system("cls");
        cout << "Game Over! Final Score: " << score << endl;
        cout << "Press any key to exit..." << endl;
        _getch();
    }
};

int main() {
    // 设置控制台窗口大小（调整窗口大小以适应新的游戏区域）
    system("mode con cols=62 lines=25");
    
    // 设置控制台字体为等宽字体
    HANDLE console = GetStdHandle(STD_OUTPUT_HANDLE);
    CONSOLE_FONT_INFOEX cfi;
    cfi.cbSize = sizeof(cfi);
    cfi.nFont = 0;
    cfi.dwFontSize.X = 8;   // 使用更小的字体尺寸
    cfi.dwFontSize.Y = 16;  // 保持高度为16
    cfi.FontFamily = FF_DONTCARE;
    cfi.FontWeight = FW_NORMAL;
    wcscpy_s(cfi.FaceName, L"Consolas");  // 使用Consolas字体
    SetCurrentConsoleFontEx(console, FALSE, &cfi);
    
    SnakeGame game;
    game.run();
    
    return 0;
} 