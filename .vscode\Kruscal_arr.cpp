#include <iostream>
#include <array>
#include <vector>
#include <algorithm>
using namespace std;

const int N = 1e5 + 10;

int n,m;
struct cmp{
    bool operator()(const auto& a, const auto &b){
        return a[2] < b[2];
    }
};

template<typename T, size_t N>

using ar = array<int, 3>;
vector<ar<int, 3>> f;
int main(){

    scanf("%d%d", &n, &m);
    while(m--){
        int u,v,w;
        scanf("%d%d%d", &u, &v, &w);
        f.push_back({u, v, w});
    }

    sort(f.begin(), f.end(), cmp());

    for (auto& edge : f){
        printf("%d %d %d\n", edge[0], edge[1], edge[2]);
    }
    return 0;

}