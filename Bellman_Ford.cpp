#include<iostream>
#include<cstring>
#include<algorithm>


using namespace std;

const int N = 510,M = 100010;
int h[N],e[M],ne[M],wx[M],idx = 0;

int n,m,k;

int d[N],backup[N];


void insert(int u,int v,int w){
    e[idx] = v;
    wx[idx] = w;
    ne[idx] = h[u];
    h[u] = idx++;
}

void bellman_ford(){
    
    d[1] = 0;
    
    for(int i = 0;i<k;++i){
        
        memcpy(backup,d,sizeof d);
        for(int ver1 = 1;ver1<=n;++ver1){

            for(int j = h[ver1];~j;j = ne[j]){
                int ver2 = e[j];
                d[ver2] = min(d[ver2],backup[ver1]+wx[j]);
            }
        }
    }
    
    if(d[n]>0x3f3f3f3f/2){
        cout << "impossible" << endl;
    }else{
        cout << d[n] << endl;
    }
}

int main(){
    
    memset(h,-1,sizeof h);
    memset(d,0x3f3f3f3f,sizeof d);
    
    scanf("%d%d%d",&n,&m,&k);
    
    for(int i = 0;i<m;++i){
        int x,y,z;
        scanf("%d%d%d",&x,&y,&z);
        insert(x,y,z);
    }
    
    bellman_ford();
    
    system("pause");
    return 0;
    
    
}