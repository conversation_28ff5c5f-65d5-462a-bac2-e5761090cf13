#include <iostream>
#include <cmath>
using namespace std;

const int N = 50;

char g[N][N]; //放置皇后的图

int n;

int x[N];

bool check(int u,int v){

    for(int i = 1;i<u;++i){
        if(x[i] == v || abs(x[i]-x[u]) == abs(i-u))return false;
    }

    return true;
}
void dfs(int u){

    if(u > n){
        for(int i = 1;i<=n;++i){
            g[i][x[i]] = 'Q';
            puts(g[i]+1);
            g[i][x[i]] = '.';
        }

        puts("");
        return;
    }
    for(int i = 1;i<=n;++i){

        x[u] = i;

        if(check(u,i)){
            dfs(u+1);
        }
    }
}

int main(int argc, char* argv[]){

    scanf("%d",&n);

    for(int i = 1;i<=n;++i){
        for(int j = 1;j<=n;++j){
            g[i][j] = '.';
        }
    }

    dfs(1);
    system("pause");

    return 0;
}