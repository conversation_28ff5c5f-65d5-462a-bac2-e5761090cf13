#include <iostream>

using namespace std;

const int N = 300;


int x[N];

// g用来存储三角形
int g[N][N];

// solution是符号三角形的数量
int n,solution;

int num;

void dfs(int u,int count){


    if(u > n){

        solution++;
        for(int i = 1;i<u;++i)printf("%d ",x[i]);

        puts("");
        return;
    }

    if(count<num/2 && u*(u-1)/2-count<num/2){

        int temp = count;

        for(int i = 0;i<=1;++i){
            x[u] = i;
            count += i;

        for(int i = 2;i<=u;++i){
            for(int j = 1;j<i;++j){
                g[i][j] = g[i-1][j] ^ g[i-1][j+1];
                count += g[i][j];
                }
            }
            dfs(u+1,count);

            count = temp;

        }

    }
}
    





int main(int argv,char *argc[]){

    scanf("%d",&n);

    num = n*(n+1)/2;

    if(num%2 == 1){
        cout << "No Solution" << endl;
    }else{

        dfs(1,0);

        cout << solution << endl;
    }

    system("pause");

    return 0;
}