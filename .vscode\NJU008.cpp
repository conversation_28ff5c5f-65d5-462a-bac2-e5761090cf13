#include<iostream>
#include<cstring>
#include<queue>
using namespace std;

const int N = 1e6 + 10, M = 2e4 + 10;
const int MAX = 1e9;
int h[M], e[N], ne[N], w[N];
int idx, n, m;
int u,v,wt;
int T;
queue<int> q;

bool check(int threshold){
	while(q.size())q.pop();
	q.push(0);
	int valid = 0;
	while(q.size()){
		int t = q.front();
		q.pop();
		valid ++;
		for(int i = h[t];~i;i=ne[i]){
			int j = e[i];
			if(w[i] >= threshold){
				q.push(j);
			}
		}
	}

	return valid <= m;
}
int find(int l,int r){
	while(l < r){
		int mid = (l + r) >> 1;
		if(check(mid)){
			r = mid;
		}else{
			l = mid + 1;
		}
	}
	return l;
}
void add(int u,int v,int wt){
	e[idx] = v;
	ne[idx] = h[u];
	w[idx] = wt;
	h[u] = idx++;
}
int main(){
	scanf("%d",&T);
	while(T--){
		memset(h, -1, sizeof h);
		memset(e, 0, sizeof e);
		memset(w, 0, sizeof w);
		memset(ne, 0, sizeof ne);
		idx = 0;
		scanf("%d%d",&n,&m);
		for(int i = 0;i<n-1;++i)scanf("%d%d%d",&u,&v,&wt),add(u,v,wt);
		int threshold = find(0, MAX);
		cout << threshold << endl;
	}
	
	return 0;
}