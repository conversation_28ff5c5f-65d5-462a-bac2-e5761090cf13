#include <iostream>
#include <cstring>

using namespace std;

const int N = 1010;

int INF = 0x3f3f3f3f;

int g[N][N];
int f[N][N][2];
int n;
int main(){

    scanf("%d",&n);

    memset(f, 0x3f, sizeof f);

    for(int i = 1; i <= n; i++){
        for(int j = 1; j <= n; j++){
            scanf("%d",&g[i][j]);
        }
    }

    for(int i = 1; i <= n; i++){
        for(int j = 1; j <= n; j++){
            if(i == 1 && j == 1){
                f[i][j][0] = g[i][j];
            }else{
                if(g[i][j]){
                    f[i][j][0] = min(f[i-1][j][0], f[i][j-1][0]) + g[i][j];
                }else{
                    f[i][j][0] = INF;
                }
            }
        }
    }

    for(int i = n; i >= 1; i--){
        for(int j = n; j >= 1; j--){
            if(i == n && j == n){
                f[i][j][1] = g[i][j];
            }else{
                if(g[i][j]){
                    f[i][j][1] = min(f[i+1][j][1], f[i][j+1][1]) + g[i][j];
                }else{
                    f[i][j][1] = INF;
                }
            }
        }
    }

    int res = INF;

    int dx[4] = {1, 0, -1, 0}, dy[4] = {0, 1, 0, -1};
    for(int i = 1; i <= n; i++){
        for(int j = 1; j <= n; j++){
            for(int k = 0; k < 4; k++){
                int x = i + dx[k], y = j + dy[k];
                if(x >= 1 && y >= 1 && x  <= n && y <= n){
                    int a = f[i][j][0], b = f[x][y][1];
                    res = min(res, max(a, b));
                }
            }
        }
    }


    if(res < INF){
        cout << res << endl;
    }else{
        cout << -1 << endl;
    }

    return 0;
}

