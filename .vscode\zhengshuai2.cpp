#include <iostream>
#include <queue>
#include <vector>
#include <cmath>

using namespace std;

int n; // 皇后的数量
vector<vector<char>> board; // 棋盘

// 检查当前位置是否与已有的皇后冲突
bool isSafe(int row, int col, const vector<int>& positions) {
    for (int i = 0; i < row; ++i) {
        // 检查列冲突或者斜线冲突
        if (positions[i] == col || abs(i - row) == abs(positions[i] - col)) {
            return false;
        }
    }
    return true;
}

// 打印当前棋盘的状态
void printBoard(const vector<int>& positions) {
    for (int i = 0; i < n; ++i) {
        for (int j = 0; j < n; ++j) {
            if (positions[i] == j) {
                cout << "Q ";
            } else {
                cout << ". ";
            }
        }
        cout << endl;
    }
}

// 使用分支限界法解决N皇后问题
void branchAndBound() {
    queue<vector<int>> q; // 队列存储当前的皇后位置
    q.push({}); // 初始状态为空

    while (!q.empty()) {
        vector<int> positions = q.front();
        q.pop();

        int row = positions.size(); // 当前已放置皇后的行数

        if (row == n) {
            // 如果所有皇后都放置完毕，打印棋盘
            printBoard(positions);
            return;
        }

        // 尝试放置皇后在当前行的每一列
        for (int col = 0; col < n; ++col) {
            if (isSafe(row, col, positions)) {
                // 如果当前位置安全，则将其添加到队列中
                vector<int> newPositions = positions;
                newPositions.push_back(col);
                q.push(newPositions);
            }
        }
    }
}

int main() {
    cin >> n;

    // 初始化棋盘
    board = vector<vector<char>>(n, vector<char>(n, '.'));

    if (n == 2 || n == 3) {
        cout << "No solution exists for N = " << n << endl;
    } else {
        branchAndBound();
    }
    system("pause");void display(void)
{
    // 清除颜色和深度缓冲区
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

    // 设置视角
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    gluPerspective(75, 1, 1, 21);
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
    gluLookAt(1, 5, 5, 0, 0, 0, 0, 0, 1);

    // 启用纹理映射
    glEnable(GL_TEXTURE_2D);

    // 绑定地球纹理
    glBindTexture(GL_TEXTURE_2D, texEarth);

    // 设置纹理坐标
    GLUquadricObj* quadric = gluNewQuadric();
    gluQuadricTexture(quadric, GL_TRUE);
    gluSphere(quadric, 1, 20, 20); // 绘制半径为1的球体，细分20x20
    gluDeleteQuadric(quadric);

    // 禁用纹理映射
    glDisable(GL_TEXTURE_2D);

    // 交换缓冲区
    glutSwapBuffers();
}

    return 0;
}
