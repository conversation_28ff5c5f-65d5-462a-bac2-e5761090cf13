#include<iostream>
#include<cstring>
#include<queue>
#include<vector>
using namespace std;

const int N = 510, M = 5010;

typedef pair<int,int> PII;

priority_queue<PII,vector<PII>,greater<PII>> heap;
int h[N],ne[M],e[M],dist[N],w[M];
int idx, n, m;
bool st[N];

void add(int u,int v,int wi){
    e[idx] = v;
    ne[idx] = h[u];
    w[idx] = wi;
    h[u] = idx++;
}

int main(){

    scanf("%d%d",&n,&m);
    memset(h, -1, sizeof h);
    memset(dist, 0x3f, sizeof dist);

    while(m--){
        int u,v,wi;
        scanf("%d%d%d",&u,&v,&wi);
        add(u,v,wi);
    }
    dist[1] = 0;
    heap.push({0, 1});
    while(heap.size()){
        PII x = heap.top();
        heap.pop();
        int distance = x.first, u = x.second;
        if(st[u])continue;
        st[u] = true;
        for(int i = h[u];~i;i=ne[i]){
            int j = e[i];
            if(!st[j]){
                dist[j] = min(dist[j], dist[u] + w[i]);
                heap.push({dist[j], j});
            }
        }
    }

    if(dist[n] < 0x3f){
        cout << dist[n] << endl;
        return 0;
    }

    cout << -1 << endl;

    return 0;
}