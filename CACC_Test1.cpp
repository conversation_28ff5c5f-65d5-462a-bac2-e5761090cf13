#include<iostream>

using namespace std;

const int N = 2e4 + 10;


bool g[N][N];


int n,a,b;

bool in(int x,int y){
    return x>=1 && x<=a && y>=1 && y<=b;
}


int main(){
    
    scanf("%d%d%d",&n,&a,&b);
    
    while(n--){
        
        int lx,ly,rx,ry;
        scanf("%d%d%d%d",&lx,&ly,&rx,&ry);
        

        if(in(lx,ly) && in(rx,ry)){
            for(int i = lx+1;i<=rx;++i){
                for(int j = ly+1;j<=ry;++j){
                    if(!g[i][j]){
                        g[i][j] = true;
                    }
                }
            }
        }else if(in(lx,ly)){
            for(int i = lx+1;i<=min(a,rx);++i){
                for(int j = ly+1;j<=min(b,ry);++j){
                    if(!g[i][j]){
                        g[i][j] = true;
                    }
                }
            }
        }else if(in(rx,ry)){
            for(int i = max(lx,0)+1;i<=rx;++i){
                for(int j = max(ly,0)+1;j<=ry;++j){
                    if(!g[i][j]){
                        g[i][j] = true;
                    }
                }
            }
        }else if(in(lx,ry)){
            for(int i = lx+1;i<=a;++i){
                for(int j = 1;j<=ry;++j){
                    if(!g[i][j]){
                        g[i][j] = true;
                    }
                }
            }
        }else if(in(rx,ly)){
            for(int i = 1;i<=rx;++i){
                for(int j = ly+1;j<=b;++j){
                    if(!g[i][j]){
                        g[i][j] = true;
                    }
                }
            }
        }else{
            continue;
        }
    }
    
    int area = 0;
    
    for(int i = 1;i<=a;++i){
        for(int j = 1;j<=b;++j){
            if(g[i][j])area++;
        }
    }
    
    cout << area << endl;

    
    system("pause");
    return 0;
    
}