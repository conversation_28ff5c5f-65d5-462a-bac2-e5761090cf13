#include<iostream>
#include<cstring>
#include<algorithm>

using namespace std;

const int N = 1010,M = 10010;

int n,m,k;

int p[N];
int cnt;
int res;

struct Edges{
    int u,v,w;
    
    bool operator < (const Edges &W)const{
        return w<W.w;
    }
}edges[M];

int find(int x){
    
    if(p[x] == x)return x;
    
    p[x] = find(p[x]);
    
    return p[x];
}

bool <PERSON>kal(){
    
    sort(edges+1,edges+1+m);
    
    for(int i = 1;i<=n;++i){
        p[i] = i;
    }
    
    for(int i = 1;i<=m;++i){
        auto t = edges[i];
        
        int u = t.u,v = t.v,w = t.w;
        
        int pu = find(u),pv = find(v);
        
        if(pu != pv){
            
            res += w;
            cnt++;
            p[pv] = pu;
            /*  cnt当然可以>n-k，但那样就不是k棵生成树了 */
            /* acwing中，由于cnt最多是n-1并且预期是n-1，所以return放在最后写是合理的，这里需要提前return*/
            if(cnt==n-k)return true;
        }
    }
    
    return false;
    
}

int main(){
    
    scanf("%d%d%d",&n,&m,&k);
    
    for(int i = 1;i<=m;++i){
        int x,y,z;
        scanf("%d%d%d",&x,&y,&z);
        edges[i] = {x,y,z};
    }
    
    
    if(Kruskal()){
        cout << res << endl;
    }else{
        cout << "No Answer" << endl;
    }
    
    system("pause");
    return 0;
}