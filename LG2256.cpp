#include<iostream>

using namespace std;

const int N = 2e4+10;

int p[N];
char str[N][N];
int find(int x){
    if(x == p[x])return x;

    p[x] = find(p[x]);

    return p[x];
}


int main(){
    // int n,m;
    // scanf("%d%d",&n,&m);
    // for(int i =1;i<=n;++i){
    //     scanf("%s",str[i][1]);
    //     p[i] = i;
    // }
    
    // while(m--){
    //     char str1[N],str2[N];
    //     scanf("%s%s",str1+1,str2+1);
    //     for(int i = 1;i<=n;++i){
    //         if(str[i])
    //     }
    // }
    //Jumping转换，我还不会做
    return 0;

}