#include<iostream>
#include<cstring>
#include<cmath>
using namespace std;

const int N = 2048;
char e[N];
char str[N];
int idx = 0;


int Type(int l,int r,char str[]){
    int sum = 0;
    for(int i = l;i<=r;++i){
        sum+=str[i] - '0';
    }

    if(0 == sum){
        return 0; //全0串
    }else if(sum == r-l+1){
        return 1; //全1串
    }else{
        return 2; //混合串
    }
}

void Search(char str[],int idx){
    if(!str[idx]){
        return;
    }

   
    Search(e,2*idx);
    Search(e,2*idx+1);
    printf("%c",e[idx]);

}
void insert(int l,int r,int mark,int idx){
    
    
    if(mark == 1){
        e[idx] = 'I';
    }else if(mark == 0){
        e[idx] = 'B';
    }else{
        e[idx] = 'F';
    }
    
    if(l == r)return;

    int mid = l + (r-l)/2;


    mark = Type(l,mid,str);
    insert(l,mid,mark,2*idx);
    mark = Type(mid+1,r,str);
    insert(mid+1,r,mark,2*idx+1);
}
int main(){
    int n;
    scanf("%d%s",&n,str+1);
    n = pow(2,n);
    insert(1,n,Type(1,n,str),1);

    Search(e,1);
    system("pause");

    return 0;

}