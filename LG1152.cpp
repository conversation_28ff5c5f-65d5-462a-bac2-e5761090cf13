#include<iostream>
#include<cstring>

using namespace std;

const int N = 1e5+1;

int n;
int q[N];
bool st[N];
int absl(int x,int y){
    return x-y>=0 ? x-y : y-x;
}
int main(){
    scanf("%d",&n);
    memset(st,false,sizeof st);
    for(int i = 1;i<=n;++i)scanf("%d",&q[i]);
    
    for(int i =1;i<n;++i){
        st[absl(q[i],q[i+1])] = true;
    }



    for(int i = 1;i<n;++i){
        if(!st[i]){
            printf("Not jolly");
            break;
        }
        
        if(st[i] && i==n-1){
            printf("Jolly");
        }
    }
    
    return 0;
    
}