#include <iostream>
#include <map>
using namespace std;

int l, r;
// 使用map来存储状态，因为状态空间可能很大
map<pair<int, int>, int> dp;

int cnt(int x, int last) {
    // 构造新的数字
    int temp = x * 10 + last;
    if (temp > r)
        return 0;
        
    // 检查状态是否已计算
    pair<int, int> state = {x, last};
    if (dp.count(state))
        return dp[state];
    
    int res = 0;
    if (last == 0) {
        res = (temp < l ? 0 : 1) + cnt(temp, 1);
    } 
    else if (last == 9) {
        res = (temp < l ? 0 : 1) + cnt(temp, 8);
    } 
    else {
        res = (temp < l ? 0 : 1) + cnt(temp, last - 1) + cnt(temp, last + 1);
    }
    
    // 存储状态
    return dp[state] = res;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    
    int T;
    cin >> T;
    while (T--) {
        cin >> l >> r;
        int t = 0;
        
        // 每个测试用例清空dp
        dp.clear();
        
        // 处理所有可能的起始数字
        for (int i = 1; i <= 9; i++) {
            t += cnt(i, i - 1);
        }
        for (int i = 1; i < 9; i++) {
            t += cnt(i, i + 1);
        }
        
        cout << t << endl;
    }
    
    return 0;
}