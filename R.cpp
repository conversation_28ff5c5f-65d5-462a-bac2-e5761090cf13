#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int path[N];

int a[3] = {2,3,6};

int n;

bool st[N];

void dfs(int u){

    if(u == n){
        for(int i = 0;i<n;++i)printf("%d ",path[i]);

        puts("");

        return;
    }

    for(int i = 0;i<n;++i){
        if(!st[i]){
            st[i] = true;
            path[u] = a[i];
            dfs(u+1);
            st[i] = false;
        }
    }
}

int main(){

    n = 3;
    
    

    dfs(0);
    system("pause");
    return 0;    
    
}