#include <iostream>
#include <set>
#include <queue>

using namespace std;

struct State {
    int a, b, c;
    State(int _a, int _b, int _c) : a(_a), b(_b), c(_c) {}
    bool operator<(const State& other) const {
        if (a != other.a) return a < other.a;
        if (b != other.b) return b < other.b;
        return c < other.c;
    }
};

set<int> possibleCValues;
set<State> visited;

void pour(int& from, int& to, int toCapacity) {
    int pourAmount = min(from, toCapacity - to);
    from -= pourAmount;
    to += pourAmount;
}

void bfs(int A, int B, int C) {
    queue<State> q;
    q.push(State(0, 0, C));
    visited.insert(State(0, 0, C));

    while (!q.empty()) {
        State current = q.front();
        q.pop();
        possibleCValues.insert(current.c);

        int a = current.a, b = current.b, c = current.c;

        // A -> B
        a = current.a; b = current.b; c = current.c;
        pour(a, b, B);
        if (visited.find(State(a, b, c)) == visited.end()) {
            q.push(State(a, b, c));
            visited.insert(State(a, b, c));
        }

        // A -> C
        a = current.a; b = current.b; c = current.c;
        pour(a, c, C);
        if (visited.find(State(a, b, c)) == visited.end()) {
            q.push(State(a, b, c));
            visited.insert(State(a, b, c));
        }

        // B -> A
        a = current.a; b = current.b; c = current.c;
        pour(b, a, A);
        if (visited.find(State(a, b, c)) == visited.end()) {
            q.push(State(a, b, c));
            visited.insert(State(a, b, c));
        }

        // B -> C
        a = current.a; b = current.b; c = current.c;
        pour(b, c, C);
        if (visited.find(State(a, b, c)) == visited.end()) {
            q.push(State(a, b, c));
            visited.insert(State(a, b, c));
        }

        // C -> A
        a = current.a; b = current.b; c = current.c;
        pour(c, a, A);
        if (visited.find(State(a, b, c)) == visited.end()) {
            q.push(State(a, b, c));
            visited.insert(State(a, b, c));
        }

        // C -> B
        a = current.a; b = current.b; c = current.c;
        pour(c, b, B);
        if (visited.find(State(a, b, c)) == visited.end()) {
            q.push(State(a, b, c));
            visited.insert(State(a, b, c));
        }
    }
}

int main() {
    int A, B, C;
    cout << "请输入三个杯子的容量 A, B, C: ";
    cin >> A >> B >> C;

    bfs(A, B, C);

    cout << "C 中水量的可能性有 " << possibleCValues.size() << " 种。" << endl;

    return 0;
}