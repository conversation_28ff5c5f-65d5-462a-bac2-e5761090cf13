#include <iostream>
#include <cstring>
#include <string>
#include <vector>
#include <algorithm>
using namespace std;

int n;
struct student{

    char name[20];
    int grade;
};


bool cmp(const student&stu1, const student&stu2){
    if(stu1.grade == stu2.grade){
        return strcmp(stu1.name, stu2.name);        
    }

    return stu1.grade > stu2.grade;
}

int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);
    cin >> n;
    vector<student> v(n);
    for(int i = 0;i<n;++i){
        cin >> v[i].name >> v[i].grade;
    }

    sort(v.begin(), v.end(), cmp);
    for(const auto&x : v){
        cout << x.name << " " << x.grade << endl;
    }

    return 0;
}