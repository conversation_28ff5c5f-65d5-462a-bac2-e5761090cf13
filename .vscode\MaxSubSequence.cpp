#include<iostream>
#include<array>
#include<vector>

using namespace std;

vector<int> f, dp;

int n, u, pos;
int main(){
    scanf("%d",&n);
    for(int i = 0;i < n;++i){
        int x;
        scanf("%d",&x);
        f.push_back(x);
    }
    dp.push_back(f[0]); // Initialize the first element
    for(int i = 1;i<n;++i){
        dp.push_back(max(dp[i-1] + f[i], f[i]));
    }

    for(int i = 0;i < n;++i){
        if(u < dp[i]){
            u = dp[i];
            pos = i;
        }
    }

    cout << u << " " << pos << endl;

    return 0;
}

