#include <iostream>
#include <cstring>

using namespace std;

const int N = 1010;
const int inf = 0x3f3f3f3f;

int h[N], e[N], ne[N];
int f[N][3];  // f[i][0]:i节点不建基站且不被覆盖
              // f[i][1]:i节点不建基站但被覆盖
              // f[i][2]:i节点建基站
int idx, n;

void add(int a, int b) {
    e[idx] = b, ne[idx] = h[a], h[a] = idx++;
}

void dfs(int u) {
    // 如果是叶子节点
    if(h[u] == -1) {
        f[u][0] = inf;    // 叶子节点不建基站且不被覆盖是不可能的
        f[u][1] = 0;      // 叶子节点不建基站但被覆盖（由父节点覆盖）
        f[u][2] = 1;      // 叶子节点建基站
        return;
    }
    
    // 初始化当前节点的状态
    f[u][0] = 0;  // 不建基站且不被覆盖
    f[u][1] = inf;// 不建基站但被覆盖
    f[u][2] = 1;  // 建基站
    
    // 遍历所有子节点
    for(int i = h[u]; ~i; i = ne[i]) {
        int j = e[i];
        dfs(j);
        
        // 状态转移
        // 1. 当前节点不建基站且不被覆盖，子节点必须建基站
        f[u][0] += f[j][2];
        
        // 2. 当前节点不建基站但被覆盖，子节点可选建基站或被覆盖
        f[u][1] = min(f[u][1], f[u][0] - f[j][2] + min(f[j][1], f[j][2]));
        
        // 3. 当前节点建基站，子节点可选任意状态
        f[u][2] += min(min(f[j][0], f[j][1]), f[j][2]);
    }
}

int main() {
    scanf("%d", &n);
    
    // 初始化
    memset(h, -1, sizeof h);
    memset(f, 0x3f, sizeof f);
    idx = 0;
    
    // 建树
    for(int i = 0; i < n; i++) {
        if(2 * i + 1 < n) {
            add(i, 2 * i + 1);
        }
        if(2 * i + 2 < n) {
            add(i, 2 * i + 2);
        }
    }
    
    // 从根节点开始DP
    dfs(0);
    
    // 输出结果
    cout << min(f[0][1], f[0][2]) << endl;
    
    return 0;
}