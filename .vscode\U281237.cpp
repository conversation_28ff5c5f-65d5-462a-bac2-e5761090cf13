#include<iostream>

using namespace std;

int n,m;

int q[12][4];
int g[2560][1440][11];
int main(){

    scanf("%d%d",&n,&m);

    for(int i = 1;i<=n;++i){
        int x1,y1,x2,y2;
        scanf("%d%d%d%d",&x1,&y1,&x2,&y2);
        q[i][0] = x1, q[i][1] = y1, q[i][2] = x2, q[i][3] = y2;
        for(int x = x1;x<=x2;++x){
            for(int y = y1;y<=y2;++y){
                g[x][y][0] += 1;
                g[x][y][g[x][y][0]] = i;
            }
        }
    }
    while(m--){

        int x,y;
        scanf("%d%d",&x,&y);
        if(g[x][y][0] == 0){
            cout << "IGNORED" << endl;
        }else{
            int tt = g[x][y][g[x][y][0]];
            cout << tt << endl;
            int pos = 0;
            int x1 = q[tt][0], y1 = q[tt][1], x2 = q[tt][2], y2 = q[tt][3];
            for(int i = x1;i<=x2;++i){
                for(int j = y1;j<=y2;++j){
                    if(g[i][j][0] == 1)continue;

                    int end = g[i][j][0];
                    for(int k = 1;k<=end;++k){
                        if(g[i][j][k] == tt){
                            pos = k;
                            break;
                        }
                    }
                    for(int k = pos;k<end;++k){
                        g[i][j][k] = g[i][j][k+1];
                    }
                    g[i][j][end] = tt;
                }
            }
        }
    }

    return 0;
}