#include<iostream>
#include<algorithm>

using namespace std;

const int N = 1e5 + 10;

int from[N],to[N],w[N],r[N];

int idx,n,m = 0;

int main(){

    scanf("%d",&m);
    for(int i = 1;i<=2*m;++i)r[i] = i;
    for(int i = 1;i<=m;++i){
        scanf("%d%d%d",&from[2*i-1],&to[2*i-1],&w[2*i-1]);
    }

    sort(r+1,r+m+1,[](int a,int b){return w[a] < w[b];});

    for(int i = 1;i<=m;++i)printf("%d ",w[r[i]]);
    system("pause");
    return 0;
}