#include<iostream>

using namespace std;

const int N = 310;
int n, m, t, sx, sy, ex, ey;

int ans;

bool avaliable;

int dx[4] = {-1,0,1,0},dy[4] = {0,1,0,-1};
char g[N][N];
bool st[N][N];
void dfs(int x, int y, int t){
    if(t <= 0)return;
    if(x == ex && y == ey){
        ans = min(ans, t);
        avaliable = true;
        return;
    }

    for(int step = 0;step < 4;++step){
        int nx = x + dx[step];
        int ny = y + dy[step];
        if(nx >= 0 && nx < n && ny >= 0 && ny < m && g[nx][ny] != '#' && !st[nx][ny]){
            st[nx][ny] = true;
            if(g[nx][ny] == '.' || g[nx][ny] == '+'){
                dfs(nx, ny, t);
            }else{
                dfs(nx, ny, t - g[nx][ny] + '0');
            }
            st[nx][ny] = false;
        }
    }
}
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    cin >> n >> m >> t;

    ans = t;
    for(int i = 0;i<n;++i){
        for(int j = 0;j<m;++j){
            cin >> g[i][j];
            if(g[i][j] == '*'){
                sx = i, sy = j;
            }
            
            if(g[i][j] == '+'){
                ex = i, ey = j;
            }
        }
    }

    st[sx][sy] = true;
    dfs(sx, sy, t);

    if(avaliable){
        cout << ans << endl;
    }else{
        cout << 0 << endl;
    }

    return 0;

}