#include<iostream>

using namespace std;

const int N = 100
 + 10;

int n,d;

typedef long long ll;

ll Q[N][N],K[N][N],V[N][N],Temp[N][N],Res[N][N];


int main(){

    scanf("%d%d",&n,&d);

    for(int i = 1;i<=n;++i){
      for(int j = 1;j<=d;++j){
        scanf("%ld",&Q[i][j]);
      }
    }

  for(int j = 1;j<=n;++j){
    for(int i = 1;i<=d;++i){
      scanf("%ld",&K[i][j]);
    }
  }

  for(int i = 1;i<=n;++i){
    for(int j = 1;j<=d;++j){
      scanf("%ld",&V[i][j]);
    }
  }

  for(int i = 1;i<=n;++i){
    for(int j = 1;j<=n;++j){
      ll sum = 0;
      for(int k = 1;k<=d;++k){
        sum += Q[i][k] * K[k][j];
      }

      Temp[i][j] = sum;
    }
  }

  for(int i = 1;i<=n;++i){
    for(int j = 1;j<=d;++j){
      ll sum = 0;
      for(int k = 1;k<=n;++k){
        sum += Temp[i][k] * V[k][j];
      }
      Res[i][j] = sum;
    }

  }

  for(int i = 1;i<=n;++i){
    int x;
    scanf("%d",&x);
    for(int j = 1;j<=d;++j){
      printf("%ld ",Res[i][j]*x);
    }
    puts("");
  }
  system("pause");
  return 0;

  
}