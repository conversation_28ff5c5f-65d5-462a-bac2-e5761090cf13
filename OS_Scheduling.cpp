#include<iostream>
#include<algorithm>
using namespace std;

const int N = 15;

int n;

int q[N];



bool cmp(int i,int j){
    
    return q[i] < q[j];
}

int FCFS(){

    int dist = 0;

    for(int i = 1;i<=n;++i){
        dist += q[i] - q[i-1] > 0 ? q[i] - q[i-1] : q[i-1] - q[i];
    }

    return dist;
}

int main(){

    scanf("%d",&n);

    for(int i = 0;i<=n;++i){
        scanf("%d",&q[i]);
    }

    printf("FCFS: %d\n",FCFS());
    system("pause");
    return 0;    
}