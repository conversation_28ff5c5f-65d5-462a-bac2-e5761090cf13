#include<iostream>
#include<cstring>


using namespace std;

const int N = 50;

int dp[N],g[N];


int n;

int log(int x){
    int bit = 0;
    while(x){
        bit++;
        x = x >> 1;
    }
    return bit;
}
int maxbit(int a[],int k,int i){

    int val = 256;

    for(int j = i-k+1;j<=i;++j){
        val = min(log(a[j]),val);
    }
    
    return val;
}
int main(){

    scanf("%d",&n);

    memset(dp,0x3f3f3f3f,sizeof dp);


    for(int i = 1;i<=n;++i)scanf("%d",&g[i]);

    dp[1] = maxbit(g,1,1)+1;

    for(int i = 2;i<=n && i<=256;++i){
        for(int k = 1;k<i;++k){
            dp[i] = min(dp[i],dp[i-k] + k * maxbit(g,k,i)+1);
        }
    }

    cout << dp[n] << endl;

    system("pause");
    return 0;
}