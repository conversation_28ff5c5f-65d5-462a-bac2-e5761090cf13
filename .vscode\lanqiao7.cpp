
#include<iostream>

using namespace std;

const int N = 50;

int g[N][N],sum[N][N];  

//sum是前缀和数组，arr[i]存储第i列的和
int arr[N],dp[N];

int n,m;

int lr,lc,rr,rc;
//记录子矩阵的边界

int res = -1e9;


// 求解最大连续子区间和
int dynamic(int arr[],int &l,int &r){

    int val = -1e9;

    dp[0] = -1e9;
    for(int i = 1;i<=m;++i){
        dp[i] = max(dp[i-1]+arr[i],arr[i]);
    }

    for(int i = 1;i<=m;++i){
        if(val<dp[i]){
            val = dp[i];
            r = i;
        }
    }
    
    int t = r;

    while(t>0 && dp[t] == dp[t-1]+arr[t])t--;
    
    l = t;

/* l,r记录了最大和的子区间的左右端点 */
    return val;

}

int main(){

    scanf("%d%d",&n,&m);
    /* n行m列的矩阵 */
    for(int i = 1;i<=n;++i){
        for(int j = 1;j<=m;++j){
            scanf("%d",&g[i][j]);
        }
    }

    //求解前缀和

    for(int j = 1;j<=m;++j){
        for(int i = 1;i<=n;++i){
            sum[i][j] = g[i][j] + sum[i-1][j];
        }
    }

    
    for(int i = 1;i<=n;++i){
        for(int j = i;j<=n;++j){
            
            // 选取[i:j]行,arr[k]记录第k列的矩阵和的值

            for(int k = 1;k<=m;++k){
                arr[k] = sum[j][k] - sum[i-1][k];
            }
            
            int l = 0,r = 0;
            if(res < dynamic(arr,l,r)){
                res = dynamic(arr,l,r);
                lr = i,rr = j,lc = l,rc = r;
            }
            
            
            
        }
    }

    cout << res << endl;
    
    printf("start row = %d\nstart column= %d\nend row = %d\nend column = %d\n",lr,lc,rr,rc);

    return 0;
}