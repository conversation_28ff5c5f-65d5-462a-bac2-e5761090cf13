#include <iostream>
#include <cstdio>
#include <string>
#include <cmath>
#include <cstring>

using namespace std;

int main(){
    string str;
    int a1 = 0, b1 = 0, c1 = 0;  //左边系数
    int a2 = 0, b2 = 0, c2 = 0;  //右边系数
    while(cin >> str){
        int len = str.size();
        bool right = false;  //判断等式左右
        bool Symbol = true;  //+号为true，-号为false
        int temp = 0;
        for(int i = 0; i < len; i++){
			if(!right){
				if(isdigit(str[i]))temp == temp*10+str[i]-'0';
				else if(str[i] == '+')Symbol  = true ;
				else if(str[i] == '-')Symbol  = false ;
				else if(str[i] == 'x'){
					if (temp == 0)temp = 1;
					if(i+1 < len&&str[i+1] == '^'){
						if(!Symbol) temp = -temp;
                        a1 = temp;
						i += 2;
					}
					else{
						if(!Symbol) temp = -temp;
                        b1 = temp;
					}
					temp = 0;
				}
				else if(str[i] == '='){
					right = true;
					if(!Symbol)c1 = -c1;
                    c1 = temp;
					temp = 0;
				}
			}

            else{  //等式右边
                if(isdigit(str[i])) temp = 10 * temp + str[i] - '0';
                else if(str[i] == '+' || str[i] == '-') Symbol = str[i] == '+';
                else if(str[i] == 'x'){
                    if(temp == 0) temp = 1;
                    if(i + 1 < len && str[i + 1] == '^'){
                        if(!Symbol) temp = -temp;
                        a2 += temp;
                        i += 2;
                    }
                    if(i + 1 < len && str[i + 1] != '^'){
                        b2 = temp;
                        if(!Symbol) b2 = -b2;
                    }
                    temp = 0;
                }
            }
        }
        if(temp != 0){
            c2 = temp;
            if(!Symbol) c2 = -c2;
        }
        double x1, x2, temp1, temp2;
        a1 -= a2;
        b1 -= b2;
        c1 -= c2;
        temp1 = b1 * b1 - 4 * a1 * c1;
        if(temp1 < 0) printf("No Solution\n");
        else{
            temp2 = sqrt(temp1);
            x1 = (-b1 + temp2) / 2 / a1;
            x2 = (-b1 - temp2) / 2 / a1;
            if(x1 < x2) printf("%.2lf %.2lf\n", x1, x2);
            else printf("%.2lf %.2lf\n", x2, x1);
        }
    }
    return 0;
}