#include <iostream>
#include <algorithm>
using namespace std;
const int N = 1e5 + 10;

int n;
int q[N];
int main() {
    scanf("%d",&n);
    int l = 0, r = n - 1;
    int ans = 0;
    for(int i = 0;i<n;++i)scanf("%d",&q[i]);
    sort(q, q + n);
    for(int i = 0;i<n;++i){
        printf("%d ",q[i]);
    }
    cout << endl;
  	if(n == 1){
        cout << q[0] << endl;
    }else if(n % 2 == 1){
        r = n - 2;
        while(l < r){
            ans = max(ans, q[l] + q[r]);
            l += 1;
            r -= 1;
        }
        ans = max(ans, q[n - 1]);
    }else{
        while(l < r){
            ans = max(ans, q[l] + q[r]);
            l += 1;
            r -= 1;
        }
    }
        
    cout << ans << endl;
        
    return 0;
}