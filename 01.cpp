#include<iostream>

using namespace std;

const int N = 10;

int w[N],v[N];

bool st[N];

int val = 0;


int n,m;

void dfs(int u,int vi,int wi){
    if(vi>m)return;

    val = max(val,wi);
    
    for(int i = 1;i<=n;++i){
        if(!st[i]){
            st[i] = true;
            dfs(u+1,vi+v[i],wi+w[i]);
            st[i] =false;
        }
    }
}
int main(){
    

    scanf("%d%d",&n,&m);
    
    for(int i = 1;i<=n;++i){
        int vi,wi;
        scanf("%d%d",&vi,&wi);
        v[i] = vi;
        w[i] = wi;
    }
    
    dfs(0,0,0);
    
    printf("%d\n",val);
    system("pause");
    return 0;
}