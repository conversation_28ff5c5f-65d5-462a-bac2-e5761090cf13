#include<iostream>
#include<cstring>
using namespace std;

const int N = 1e4 + 10;

int w[N], v[N], dp[N];
int n, m;

int main(){
	
	ios::sync_with_stdio(false);
	cin.tie(0);
	
	while(cin >> n >> m){
		for(int i = 1;i<=n;++i){
			cin >> w[i] >> v[i];
		}
		memset(dp, 0, sizeof dp);
		for(int i = 1;i<=n;++i){
			for(int j = w[i];j<=m;++j){
				dp[j] = max(dp[j], dp[j-w[i]] + v[i]);
			}
		}
		
		cout << dp[m] << endl;
		
	}

    return 0;
	
}