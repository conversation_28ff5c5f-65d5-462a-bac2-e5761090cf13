#include<iostream>

using namespace std;

const int N = 10;


int n,m1,m2;

int v[N];

bool st[N];

bool res;

int val = 0;

int all_v;

void dfs(int v_curr){

    if(v_curr> m1 )return;

    if(all_v - v_curr <= m2){
        res = true;
        return;
    }

    
    for(int i = 1;i<=n;++i){
        if(!st[i]){
            st[i] = true;
            dfs(v_curr+v[i]);
            st[i] =false;
        }
    }

}
int main(){
    

    scanf("%d%d%d",&n,&m1,&m2);
    
    for(int i = 1;i<=n;++i){
        int vi;
        scanf("%d",&vi);
        v[i] = vi;
        all_v += vi;
    }
    

    dfs(0);
    
    if(res){
        puts("Yes");
    }else{
        puts("No");
    }

    system("pause");
    return 0;
}