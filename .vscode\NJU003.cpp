#include<iostream>
#include<string>
#include<cstring>


using namespace std;

const int N = 110;

char a[N], b[N];
int dp[N][N];
int len, ed;
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    while(cin >> a + 1 >> b + 1){
        int n = strlen(a + 1);
        int m = strlen(b + 1);
        len = 0;
        ed = 0;
        memset(dp, 0, sizeof dp);
        for(int i = 1;i<=n;++i){
            for(int j = 1;j<=m;++j){
                if(a[i] == b[j]){
                    dp[i][j] = dp[i-1][j-1] + 1;
                    if(dp[i][j] >= len){
                        len = dp[i][j];
                        ed = i;
                    }
                }
            }
        }

        cout << len << endl;
        if(len){
            for(int i = ed - len + 1;i<=ed;++i)cout << a[i];
            cout << endl;
        }
    }

    return 0;



}