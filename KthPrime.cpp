#include <iostream>
#include <vector>

// 判断一个数是否为质数
bool isPrime(int num) {
    if (num < 2) return false;
    for (int i = 2; i * i <= num; ++i) {
        if (num % i == 0) return false;
    }
    return true;
}

// 找到第 k 个质数
int findKthPrime(int k) {
    int count = 0;
    int num = 2;
    while (true) {
        if (isPrime(num)) {
            ++count;
            if (count == k) return num;
        }
        ++num;
    }
}

int main() {
    int k;
    while (std::cin >> k) {
        std::cout << findKthPrime(k) << std::endl;
    }
    return 0;
}