#include<iostream>
#include<cstring>
using namespace std;

const int N = 310;

int dp[N][N];
int w[N];
int n;
int main(){
	memset(dp, 0x3f, sizeof dp);
	scanf("%d",&n);
	for(int i = 1;i<=n;++i){
        int x;
		scanf("%d",&x);
        w[i] = w[i - 1] + x;
	}
	for(int i = n;i>=1;--i){
		for(int j = i;j<=n;++j){
			for(int k = i;k<=j;++k){
				if(i == j)dp[i][j] = 0;
                else{
                    dp[i][j] = min(dp[i][j], dp[i][k] + dp[k+1][j] + w[j] - w[i - 1]);
                }
			}
		}
	}
	cout << dp[1][n] << endl;
	
	return 0;
}