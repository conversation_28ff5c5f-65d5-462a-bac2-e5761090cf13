#include<iostream>
#include<vector>
using namespace std;

const int N = 5e5 + 10;

int n, m, cnt;
int p[N];
vector<int> v;
void init(){
    for(int i = 1;i<=n;++i)p[i] = i;
}

int find(int x){
    if(p[x] == x)return x;

    p[x] = find(p[x]);

    return p[x];
}
int main(){

    while(cin >> n >> m){
        init();
        v.clear();
        while(m--){
            int x, y;
            cin >> x >> y;
            int px = find(x), py = find(y);
            if(px == py){
                cout << "Yes" << endl;
            }else{
                p[py] = px;
                cout << "No" << endl;
            }
        }

        for(int i = 1;i<=n;++i){
            if(p[i] == i){
                v.push_back(i);
            }
        }

        cout << v.size() << endl;
        for(vector<int>::iterator i = v.begin() ; i != v.end(); ++i){
            cout << *i << " ";
        }

        cout << endl;
    }

    return 0;
}