#include<iostream>
#include<stack>
using namespace std;

typedef pair<int,int> PII;

const int N = 50;  //我们假设连乘的矩阵数不会超过50

int dp[N][N];      //dp[i][j]表示[i:j]区间内矩阵相乘的所有运算次数的最小值

int n;            //参与连乘的矩阵数

PII p[N];         //存储矩阵的行数和列数

stack<int> st;

int main(){

    scanf("%d",&n);   //输入矩阵的个数

    for(int i = 1;i<=n;++i){
        
        int r,c;

        scanf("%d%d",&r,&c);

        p[i] = {r,c};
    }

/*

区间的左端点从n开始取：
因为我们更新dp[i][j]的时候需要用到dp[i][k]和dp[k+1][j]

也就是保证长度更小的区间优先被计算

那么左端点从n->0，就能保证计算[i:j]时，[i:j]所有的子区间已经被计算过

*/
    for(int i = n;i>0;--i){
        for(int j = i;j<=n;++j){
            if(i == j){
                dp[i][j] = 0; //只有一个矩阵的时候运算次数为0
            }else{
                dp[i][j] = 1e9; //给dp[i][j]先赋一个超级大的值，保证min函数能起作用
                for(int k = i;k<j;++k){
                    if(dp[i][j]>dp[i][k]+dp[k+1][j]+p[i].first*p[k].second*p[j].second){
                        
                        dp[i][j]=dp[i][k]+dp[k+1][j]+p[i].first*p[k].second*p[j].second;

                        st.push(k);

                    }

                }
            }
        }
    }


    printf("%c",'(');
    for(int i = 1;i<=n;++i){
        if(!st.empty() && st.top() == i){

            printf("%c%c",')','(');

            st.pop();
        }

        printf(" %d ",i);
    }

    printf("%c\n",')');

    cout << dp[1][n] << endl;  //输出全区间的最小运算次数

    system("pause");
    return 0;


}
