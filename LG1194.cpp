#include<iostream>
#include<cstring>
#include<algorithm>

using namespace std;

const int N = 510*510;

struct Edges{
    int u,v,w;

    bool operator < (const Edges &W)const{
        return w<W.w;
    }
}edges[N];

int p[N];

int cnt,A,m,res = 0;

int idx = 0;

int find(int x){
    if(p[x] == x)return x;

    p[x] = find(p[x]);

    return p[x];
}
int Kruskal(){

    for(int i = 1;i<=m;++i)p[i] = i;

    sort(edges+1,edges+1+idx);

    for(int i = 1;i<=idx;++i){
        int u = edges[i].u;
        int v = edges[i].v;
        int w = edges[i].w;

        int pu = find(u),pv = find(v);

        if(pu != pv && w<A){
            p[pv] = pu;
            cnt++;
            res += w;
        }
    }

    cnt = m - cnt;
    while(cnt--){
        res += A;
    }

    return res;
}


int main(){
    scanf("%d%d",&A,&m);

    
    for(int i = 1;i<=m;++i){

        for(int j = 1;j<=m;++j){
            int x;
            scanf("%d",&x);
            if(!x)x = A;
            edges[++idx] = {i,j,x};
        }
    }

    cout << Kruskal() << endl;

    system("pause");
    return 0;
}