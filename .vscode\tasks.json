{
    "version": "2.0.0",
    "tasks": [{
        "type": "cppbuild",
        "label": "C/C++: g++.exe 生成活动文件",
        "command": "E:\\gcc\\ucrt64\\bin\\g++.exe", /*修改成自己bin目录下的g++.exe，这里的路径和电脑里复制的文件目录有一点不一样，这里是两个反斜杠\\*/
        "args": [
            "-fdiagnostics-color=always",
            "-g",
            "${file}",
            "-o",
            "${fileDirname}\\${fileBasenameNoExtension}.exe"
        ],
        "options": {
            "cwd": "${fileDirname}"
        },
        "problemMatcher": [
            "$gcc"
        ],
        "group": {
            "kind": "build",
            "isDefault": true
        },
        "detail": "调试器生成的任务。"
    },
        {
            "label": "build",
            "type": "shell",
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "windows": {
                "command": "powershell",
                "args": [
                    "-c",
                    "mingw32-make"
                ]
            },
            "linux": {
                "command": "bash",
                "args": [
                    "-c",
                    "make"
                ]
            },
            "osx": {
                "command": "bash",
                "args": [
                    "-c",
                    "make"
                ]
            }
        },
        {
            "label": "build & run",
            "type": "shell",
            "windows": {
                "command": "powershell",
                "args": [
                    "-c",
                    "'mingw32-make run'"
                ]
            },
            "linux": {
                "command": "bash",
                "args": [
                    "-c",
                    "'make run'"
                ]
            },
            "osx": {
                "command": "bash",
                "args": [
                    "-c",
                    "'make run'"
                ]
            }
        },
        {
            "label": "clean",
            "type": "shell",
            "windows": {
                "command": "powershell",
                "args": [
                    "-c",
                    "'mingw32-make clean'"
                ]
            },
            "linux": {
                "command": "bash",
                "args": [
                    "-c",
                    "'make clean'"
                ]
            },
            "osx": {
                "command": "bash",
                "args": [
                    "-c",
                    "'make clean'"
                ]
            }
        }
    ]
}