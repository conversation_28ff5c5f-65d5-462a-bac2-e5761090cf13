#include<iostream>
#include<cstring>
using namespace std;

const int N = 100010;

int p[N];

int find(int x){
    if(x == p[x])return x;

    p[x] = find(p[x]);

    return p[x];
}


void insert(int u,int v){
    if(find(u) != find(v)){
        if(find(v)>=find(u)){
            p[u] = find(v);
        }
    }
}


int main(){
    int n,m;
    scanf("%d%d",&n,&m);
    
    for(int i = 1;i<=n;++i){
        p[i] = i;
    }


    while(m--){
        int u,v;
        scanf("%d%d",&u,&v);
        insert(u,v);
    }

    /* debug */
    for(int i = 1;i<=n;++i)printf("%d ",find(i));




    return 0;

}