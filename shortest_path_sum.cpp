#include <iostream>
#include <vector>
#include <climits>
#include <algorithm>

using namespace std;

const int INF = 1e9;

// Floyd-Warshall算法计算所有点对之间的最短距离
vector<vector<int>> floyd_warshall(const vector<vector<int>>& graph, int n, int excluded_vertex = -1) {
    vector<vector<int>> dist(n, vector<int>(n, INF));
    
    // 初始化距离矩阵
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            if (i == j) {
                dist[i][j] = 0;
            } else if (excluded_vertex != -1 && (i == excluded_vertex || j == excluded_vertex)) {
                // 如果涉及被排除的顶点，设为无穷大
                dist[i][j] = INF;
            } else {
                dist[i][j] = graph[i][j];
            }
        }
    }
    
    // Floyd-Warshall算法
    for (int k = 0; k < n; k++) {
        if (excluded_vertex != -1 && k == excluded_vertex) continue;
        for (int i = 0; i < n; i++) {
            if (excluded_vertex != -1 && i == excluded_vertex) continue;
            for (int j = 0; j < n; j++) {
                if (excluded_vertex != -1 && j == excluded_vertex) continue;
                if (dist[i][k] != INF && dist[k][j] != INF && 
                    dist[i][k] + dist[k][j] < dist[i][j]) {
                    dist[i][j] = dist[i][k] + dist[k][j];
                }
            }
        }
    }
    
    return dist;
}

// 计算剩余点之间所有最短距离的和
long long calculate_sum_of_shortest_distances(const vector<vector<int>>& graph, int n, int excluded_vertex = -1) {
    vector<vector<int>> dist = floyd_warshall(graph, n, excluded_vertex);
    long long total_sum = 0;
    
    for (int i = 0; i < n; i++) {
        if (excluded_vertex != -1 && i == excluded_vertex) continue;
        for (int j = i + 1; j < n; j++) {  // 只计算上三角，避免重复
            if (excluded_vertex != -1 && j == excluded_vertex) continue;
            if (dist[i][j] != INF) {
                total_sum += dist[i][j];
            }
        }
    }
    
    return total_sum;
}

int main() {
    ios::sync_with_stdio(false);
    cin.tie(nullptr);
    
    int n;
    cin >> n;
    
    vector<vector<int>> graph(n, vector<int>(n));
    for (int i = 0; i < n; i++) {
        for (int j = 0; j < n; j++) {
            cin >> graph[i][j];
        }
    }
    
    long long total_result = 0;
    
    // 依次移除第 i 个点（1-based转换为0-based）
    for (int i = 0; i < n; i++) {
        // 移除第 i+1 个点（在0-based中是第 i 个点）
        long long sum_distances = calculate_sum_of_shortest_distances(graph, n, i);
        total_result += sum_distances;
        
        // 调试输出（提交时可以注释掉）
        // cout << "移除第 " << (i+1) << " 个点后，剩余点间最短距离之和: " << sum_distances << endl;
    }
    
    cout << total_result << endl;
    system("pause");
    return 0;
}
