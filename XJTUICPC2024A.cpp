#include<iostream>
#include<cstring>
#include<queue>
using namespace std;

const int N = 100010;

int h[N],e[N],ne[N],idx = 0;
bool st[N];
queue<int> q;

void insert(int u,int v){
    e[idx] = v;
    ne[idx] = h[u];
    h[u] = idx++;
}
int main(){

    int n,m;
    scanf("%d%d",&n,&m);
    memset(h,-1,sizeof h);
    while(m--){
        int a,b;
        scanf("%d%d",&a,&b);
        insert(a,b);
    }

    for(int i = 1;i<=n;++i){

        int res = i;
        q.push(i);
        while(q.size()){
            for(int u = h[q.front()];~u;u = ne[u]){
                if(!st[e[u]]){
                    q.push(e[u]);
                    st[e[u]] = true;
                }
            }
            res = max(res,q.front());
            st[q.front()] = false;
            q.pop();
        }

        printf("%d ",res);
    }


    return 0;

}