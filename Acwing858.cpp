#include<iostream>
#include<cstring>
#include<queue>

using namespace std;

typedef pair<int,int> PII;
const int N = 510,M = 1e5+10;
int res = 0;
int n,m;


priority_queue<PII,vector<PII>,greater<PII>> heap;

int dist[N];

bool st[N];

int e[2*M],ne[2*M],h[N],wgt[2*M],idx = 0;

void insert(int u,int v,int w){
    e[idx] = v;
    ne[idx] = h[u];
    wgt[idx] = w;
    h[u] = idx++;
}

int prime(){
    
    dist[1] = 0;
    heap.push({0,1});
    
    while(heap.size()){
        auto t = heap.top();
        heap.pop();
        
        int ver = t.second;
        
        if(st[ver])continue; //不可少，已进入集合的元素不再做操作
        
        res += t.first;

        st[ver] = true;
        
        for(int i = h[ver];~i;i=ne[i]){
            int j = e[i];
            if(!st[j]){
                dist[j] = min(dist[j],wgt[i]);
                heap.push({dist[j],j});
            }
        }
    }

    for(int i = 1;i<=n;++i){
        if(!st[i])return false;
    }
    
    return true;
}

int main(){
    
    memset(h,-1,sizeof h);
    memset(dist,0x3f3f3f3f,sizeof dist);

    
    scanf("%d%d",&n,&m);
    
    while(m--){
        int x,y,z;
        scanf("%d%d%d",&x,&y,&z);
        insert(x,y,z);
        insert(y,x,z);
    }
    
    if(prime()){
        printf("%d",res);
    }else{
        printf("impossible");
    }
    
    system("pause");
    return 0;
}