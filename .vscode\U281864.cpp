#include<iostream>
#include<algorithm>
using namespace std;

const int N = 20;

int x, idx, m;
long long seq[100];
bool col[N], dg[N], udg[N];
char g[N][N];
void dfs(int u){
    if(u == 8){
        x = 0;
        for(int i = 0;i<8;++i){
            for(int j = 0;j<8;++j){
                if(g[i][j] == '#'){
                    x = x * 10 + j + 1;
                }
            }
        }
        seq[idx++] = x;
        return;
    }

    for(int i = 0;i<8;++i){
        if(!col[i] && !dg[u+i] && !udg[7+i-u]){
            col[i] = dg[u+i] = udg[7+i-u] = true;
            g[u][i] = '#';
            dfs(u+1);
            col[i] = dg[u+i] = udg[7+i-u] = false;
            g[u][i] = '.';
        }
    }
}
int main(){

    for(int i = 0;i<8;++i){
        for(int j = 0;j<8;++j){
            g[i][j] = '.';
        }
    }
    cin >> m;
    dfs(0);

    sort(seq, seq + 92);
    while(m--){
        int pos;
        cin >> pos;
        cout << seq[pos-1] << endl;
    }

    return 0;
}