#include<iostream>
#include<cstring>
using namespace std;

const int N = 3010;

int dp[N][2];  
// dp[N][0]:不包含节点N的基站数量
// dp[N][1]:包含节点N的基站数量

int rt[N];
int idx;

int father(int x){
    if(x == 0) return -1;
    return (x - 1) / 2;
}
int left(int x){
    return 2 * x + 1;
}

int right(int x){
    return 2 * x + 2;
}
int main(){
	
    memset(rt, -1, sizeof rt);
    char c;
	while(cin >> c){
        if(c == 'N'){
            rt[idx++] = -1;
        }else{
            rt[idx++] = c - '0';
        }
    }
    
    for(int i = 0;i < idx;++i){
        
    }
	return 0;
}