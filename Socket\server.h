#ifndef SERVER_H
#define SERVER_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <dirent.h>
#include <sys/stat.h>
#include <fcntl.h>
#include "message.h"

// 服务器状态
#define WAITING 0

// 函数声明
int init_udp_socket(int port);
void handle_ls_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len);
void handle_send_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len, Cmd_Msg_T cmd_msg);
void handle_remove_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len, Cmd_Msg_T cmd_msg);
void handle_rename_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len, Cmd_Msg_T cmd_msg);
void handle_shutdown_command(int sockfd, struct sockaddr_in client_addr, socklen_t addr_len);
void send_cmd_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Cmd_Msg_T cmd_msg);
void send_data_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Data_Msg_T data_msg);

#endif // SERVER_H
