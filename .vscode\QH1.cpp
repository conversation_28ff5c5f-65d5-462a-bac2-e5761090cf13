#include<iostream>
#include<algorithm>
using namespace std;

const int N  = 510;

int q[N];

int n, ans;
int main(){

    scanf("%d",&n);
    for(int i = 0;i<n;++i)scanf("%d",&q[i]);

    sort(q, q + n);

    cout << endl;
    int l = 0, r = n - 1;
    while(l < r){
        if(q[l] + q[r] == 0){
            ans += 1;
            l += 1;
            r -= 1;
        }else if(q[l] + q[r] < 0){
            l += 1;
        }else{
            r -= 1;
        }
    }

    cout << ans << endl;

    return 0;
}