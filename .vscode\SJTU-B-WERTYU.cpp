#include<iostream>
#include<cstring>
#include<string>
#include<unordered_map>
using namespace std;

unordered_map<char, char> mp ={
    {'W','Q'},
    {'S','A'},
    {'X','Z'},
    {'E','W'},
    {'D','S'},
    {'C','X'},
    {'R','E'},
    {'F','D'},
    {'V','C'},
    {'T','R'},
    {'G','F'},
    {'B','V'},
    {'Y','T'},
    {'H','G'},
    {'N','B'},
    {'U','Y'},
    {'J','H'},
    {'M','N'},
    {'I','U'},
    {'K','J'},
    {',','M'},
    {'O','I'},
    {'L','K'},
    {';','L'},
    {'.',','},
    {'P','O'},
    {'[','P'},
    {']','['},
    {'\'',';'},
    {'/','.'},
    {'\\',']'},
    {'1','`'},
    {'2','1'},
    {'3','2'},
    {'4','3'},
    {'5','4'},
    {'6','5'},
    {'7','6'},
    {'8','7'},
    {'9','8'},
    {'0','9'},
    {'-','0'},
    {'=','-'},
    {'2','1'},
    {'3','2'},
    {'4','3'},
    {'5','4'},
    {'6','5'},
    {'7','6'},
    {'8','7'},
    {'9','8'},
    {'0','9'},
    {'-','0'},
    {'=','-'}
};
string s;
char ns[500];
int main(){
    
    getline(cin ,s);
    

    for(int i = 0;i<s.size();++i){
        if(mp.find(s[i]) != mp.end()){
            ns[i] = mp[s[i]];
        }else{
            ns[i] = s[i];
        }
    }
    cout << ns << endl;
    return 0;
}