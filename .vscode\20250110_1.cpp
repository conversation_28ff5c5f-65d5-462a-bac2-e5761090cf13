#include<iostream>
#include <string>

using namespace std;

const int N = 1e5+10;


int q[N];

string input;

char c[5];
void build(){


    getline(cin, input);


    int i = -1;

    for (char c : input){

        if(c == ' ')continue;

        if(isdigit(c) && q[++i] == 0){

            q[i] = c - '0';

        }else if(c == 'N'){

            q[++i] = -1;

            q[2 * i + 1] = -1;

            q[2 * i + 2] = -1;
        }else{
            continue;
        }
    }

    printf("%d\n",i);

    for(int j = 0; j < i ;++j){
        
        if(q[j] > 0){

            if(q[2 * j + 1] == 0)q[2 * j + 1] = -1;

            if(q[2 * j + 2 == 0])q[2 * j + 2] = -1;
        }
    }

}
int main(){


   build();

   cout << input << endl;

   for(int i = 0; i <= 10; i++)printf("%d ",q[i]);
   system("pause");

   return 0;
}