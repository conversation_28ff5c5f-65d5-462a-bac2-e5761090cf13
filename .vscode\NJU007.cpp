#include<iostream>
#include<cstring>
using namespace std;

const int N = 2010;

char a[N], b[N], p[N];
int dp[N][N];
int n, ans;
int main(){
	
	scanf("%s",a+1);
	scanf("%d",&n);
	while(n--){
		memset(dp, 0, sizeof dp);
        memset(b, 0, sizeof b);
		scanf("%s",b+1);
		int ls = strlen(a+1), lp = strlen(b+1);
		for(int i = 1;i<=ls;++i){
			for(int j = 1;j<=lp;++j){
				dp[i][j] = max(dp[i-1][j], dp[i][j-1]);
				if(a[i] == b[j]){
					dp[i][j] = max(dp[i-1][j-1] + 1, dp[i][j]);
				}
			}
		}
		if(ans < dp[ls][lp]){
			ans = dp[ls][lp];
			strcpy(p+1, b+1);
		}
	}
	
	cout << (p + 1) << endl;
    cout << ans << endl;
	
	return 0;
	
}