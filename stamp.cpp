#include<iostream>
#include<cstring>
using namespace std;

const int N = 50;


int n,m,r,subr;

int x[N],y[N];
void k(int u,int sum,int x[],int pos,int p[]){

    p[sum] = min(p[sum],u);

    subr = max(subr,sum);

    if(u == m)return;

    for(int i = 1;i<=pos;++i){
        
        k(u+1,sum+x[i],x,i,p);

        k(u+1,sum,x,i,p);
    }

}

int continous(){
    for(int i = 1;i<=subr;++i){
        if(y[i]==0x3f3f3f3f)return i-1;
    }

    return subr;
}

int main(){

    memset(y,0x3f3f3f3f,sizeof y);
    scanf("%d%d",&n,&m);
    
    subr = 1;



    for(int i = 1;i<=n;++i){
        int temp = 0;
        int id = 0;

       
        int res[N];
        for(int k = 1;k<=subr;++k)res[k] = y[k];
        for(int j = x[i-1]+1;j<=r+1;++j){
            x[i] = j;

            int arr[N];
            for(int k = 1;k<=subr;++k)arr[k] = res[k];
            k(0,0,x,i,arr);
        
            subr = continous();

            if(subr>temp){
                temp = subr;
                id = j; 
                for(int k = 1;k<=subr;++k)y[k] = arr[k];
            }
        }
        x[i] = id;
        r = temp;

    

    }


    for(int i = 1;i<=n;++i)cout << x[i] << " ";

    printf("\n%d",r);
    system("pause");

    return 0;
    

}