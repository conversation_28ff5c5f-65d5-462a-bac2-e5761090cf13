#include<iostream>
#include<algorithm>

using namespace std;

const int N = 1e5 + 10;

int res = 0;

int dp[N];

int q[N];

int main(){

    int idx = 0;

    int x;
    while(cin >> x)q[++idx] = x;
    q[0] = 5e4 + 10;

    for(int i = 1;i<=idx;++i){

        for(int j = 0;j<i;++j){

            if(q[i] <= q[j]){
                dp[i] = max(dp[i],dp[j] + 1);
            }
        }
    }


    for(int i = 1;i<=idx;++i){
        res = max(res,dp[i]);
    }

    cout << res << endl;
    system("pause");
    return 0;

}