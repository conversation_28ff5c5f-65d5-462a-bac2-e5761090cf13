{"name": "Local: QH1", "url": "e:\\acm\\.vscode\\.vscode\\QH1.cpp", "tests": [{"id": 1746952055968, "input": "5\n1 2 3 -1 -2", "output": "2"}, {"id": 1746952138417, "input": "6\n-3 -1 4 3 6 5", "output": "1"}, {"id": 1746952163377, "input": "7\n-4 -5 -7 3 5 7 6", "output": ""}], "interactive": false, "memoryLimit": 1024, "timeLimit": 3000, "srcPath": "e:\\acm\\.vscode\\.vscode\\QH1.cpp", "group": "local", "local": true}