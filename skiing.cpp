#include<iostream>

using namespace std;

const int N = 310;
int dx[4] = {-1,0,1,0},dy[4] = {0,1,0,-1};
int h[N][N], f[N][N];
int n, m, res;

int dp(int x,int y){
    if(f[x][y] > 1)return f[x][y];

    for(int t = 0;t<4;++t){
        int nx = x + dx[t], ny = y + dy[t];
        if(nx >= 0 && nx < n && ny >= 0 && ny < m && h[nx][ny] < h[x][y]){
            f[x][y] = max(f[x][y],dp(nx,ny) + 1);
        }
    }
    return f[x][y];
}

int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    cin >> n >> m;

    for(int i = 0;i<n;++i){
        for(int j = 0;j<m;++j){
            cin >> h[i][j];
            f[i][j] = 1;
        }
    }

    for(int i = 0;i<n;++i){
        for(int j = 0;j<m;++j){
            res = max(res, dp(i,j));
        }
    }

    cout << res << endl;

    return 0;


}