#ifndef CLIENT_H
#define CLIENT_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <fcntl.h>
#include "message.h"

// 函数声明
int init_udp_socket();
void handle_ls_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len);
void handle_send_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len, char* filename);
void handle_remove_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len, char* filename);
void handle_rename_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len, char* old_filename, char* new_filename);
void handle_shutdown_command(int sockfd, struct sockaddr_in server_addr, socklen_t addr_len);
void send_cmd_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Cmd_Msg_T cmd_msg);
void send_data_msg(int sockfd, struct sockaddr_in addr, socklen_t addr_len, Data_Msg_T data_msg);

#endif // CLIENT_H
