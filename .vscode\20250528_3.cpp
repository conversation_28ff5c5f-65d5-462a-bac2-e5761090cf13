#include <iostream>
#include <queue>
using namespace std;

const int N = 1010;
typedef pair<int, int> PII;
int g[N][N], dist[N][N];
bool st[N][N];
int tt;
int n, m, ans;
int xb, yb, xp, yp;
int peak = -1, bottom = 101;
queue<PII> q;
int dx[4] = {-1, 0, 1, 0};
int dy[4] = {0, -1, 0, 1};
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    cin >> tt;
    cin >> n >> m;
    for(int i = 0;i<n;++i){
        for(int j = 0;j<m;++j){
            cin >> g[i][j];
            if(bottom > g[i][j]){
                bottom = g[i][j];
                xb = i;
                yb = j;
            }

            if(peak < g[i][j]){
                peak = g[i][j];
                xp = i;
                yp = j;
            }
        }
    }

    q.push({xb, yb});
    st[xb][yb] = true;
    while(q.size()){
        auto t = q.front();
        int x = t.first, y = t.second;
        q.pop();
        if(x == xp && y == yp){
            cout << dist[x][y] << endl;
            return 0;
        }
        for(int i = 0;i<4;++i){
            int nx = x + dx[i];
            int ny = y + dy[i];
            if(nx >=0 && nx < n && ny >= 0 && ny<m && !st[nx][ny] &&  abs(g[nx][ny] - g[x][y]) <=tt){
                q.push({nx, ny});
                st[nx][ny] = true;
                dist[nx][ny] = dist[x][y] + 1;
            }
        }

    }

    cout << -1 << endl;
    return 0;
}