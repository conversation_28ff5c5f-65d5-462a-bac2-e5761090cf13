#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int q[5] = {3,1,8,9,0};

void Qsort(int q[],int l,int r){

    if(l>=r)return;

    int mid = (l+r)>>1;

    int i = l - 1;
    int j = r + 1;

    int x = q[mid];

    while(i<j){
        while(q[++i]>x);
        while(q[--j]<x);

        if(i<j){
            swap(q[i],q[j]);
        }
    }

    Qsort(q,l,j);
    Qsort(q,j+1,r);
}
int main(){

    Qsort(q,0,4);

    for(int i = 0;i<5;++i)printf("%d ",q[i]);

    system("pause");

    return 0;


}