#ifndef MESSAGE_H
#define MESSAGE_H

#include <stdint.h>

// 定义常量
#define FILE_NAME_LEN 128
#define DATA_BUF_LEN 3000

// 定义命令类型
#define CMD_LS 0
#define CMD_SEND 1
#define CMD_REMOVE 2
#define CMD_RENAME 3
#define CMD_QUIT 4
#define CMD_SHUTDOWN 5
#define CMD_ACK 6

// 命令消息结构体
typedef struct {
    uint8_t command; // 命令类型
    char filename[FILE_NAME_LEN]; // 文件名
    uint32_t size; // 大小
    uint16_t port; // 端口
    uint16_t error; // 错误码
} Cmd_Msg_T;

// 数据消息结构体
typedef struct {
    char data[DATA_BUF_LEN]; // 数据
} Data_Msg_T;

#endif // MESSAGE_H
