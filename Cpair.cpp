#include<iostream>

using namespace std;

const int N = 1e5 + 10;

int dist = 0x3f3f3f3f;


int q[N] = {0,2,3,8,10,14};

int abs(int q[],int l,int r){

        return q[l] > q[r] ? q[l] - q[r] : q[r] - q[l];
}

int dis(int q[],int l,int r){

        if(r<=l)return 0x3f3f3f3f;
        if(r-l==1){
                return abs(q,l,r);
        }

        int mid = (l+r)>>1;

        dist = min(dist,abs(q,mid,mid+1));

        dist = min(dist,dis(q,l,mid));

        dist = min(dist,dis(q,mid+1,r));

        return 0;
}

int main(){


        dis(q,0,5);

        cout << dist << endl;

        return 0;
}