#include<iostream>

using namespace std;

const int N = 20;


bool st[N];

int M1[N],M2[N],path[N];

int f1,f2,n;

int res = 1e9;

int ans[N];
void dfs(int u,int f1,int f2,int total){
   
    if(u == n){
            if(total<res){
                res = total;
                for(int i = 0;i<u;++i){
                    ans[i] = path[i];
                }
            }
            return;
        }


    for(int i = 1;i<=n;++i){
        if(!st[i]){
            
            st[i] = true;
            

            if(f2<=f1 + M1[i]){
                path[u] = i;
                dfs(u+1,f1+M1[i],f1+M1[i]+M2[i],total+f1+M1[i]+M2[i]);

            }else{

                path[u] = i;
                dfs(u+1,f1+M1[i],f2+M2[i],total+f2+M2[i]);

            }

            st[i] = false;
        }
    }
}

int main(){


    scanf("%d",&n);

    for(int i = 1;i<=n;++i){
        scanf("%d%d",&M1[i],&M2[i]);
    }

    dfs(0,0,0,0);

    cout << res << endl;

    for(int i = 0;i<n;++i){
        printf("%d ",ans[i]);
    }
    system("pause");

}