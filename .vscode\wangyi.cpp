#include <iostream>
#include <algorithm>
using namespace std;

const int N =  1e5 + 10;

int t, c, n, m;
int a[N], b[N];
int p;
bool st[N];
typedef pair<int, int> PII;
PII u[N];
// 大于等于左值的最小下表
int find(int x, int q[], int n) {
    int l = 1;
    int r = n;
    int mid;
    while (l < r) {
        mid = (l + r) / 2;
        if (q[mid] >= x) r = mid;
        else l = mid + 1;
    }

    return l;
}
// 小于等于右值的最大下表
int findl(int x, int q[], int n) {
    int l = 1;
    int r = n;
    int mid;
    while (l < r) {
        mid = (l + r + 1) / 2;
        if (q[mid] < x) l = mid;
        else r = mid - 1;
    }

    return l;
}
int main() {
    scanf("%d", &p);
    while (p--) {
        scanf("%d%d%d%d", &t, &c, &n, &m);

        for (int i = 1; i <= n; ++i) {
            scanf("%d", &a[i]);
        }
        for (int i = 1; i <= m; ++i)scanf("%d", &b[i]);
        // a[1],
        int ans = 0;
        for (int j = 1; j <= n; ++j) {
                int pos1, pos2;
                pos1 = find(a[j], b, m), pos2 = findl(a[j] + t, b, m);
                // 下一个坐标
                // int net = find(a[j] + t + c, a, n);
                if (a[j] <= b[pos1] and a[j] + t > b[pos2])u[j] = {pos2 - pos1 + 1, j};
            }

            sort(u + 1, u + n + 1);
            
            for(int i = n;i > 0;--i){
                auto t = u[i];
                int ans = 0;
        
                }
            }
        }

        return 0;
}