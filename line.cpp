#include<iostream>
#include<queue>
#include<cstring>


using namespace std;

const int N = 50;


typedef pair<int,int> PII;

int d[N][N],g[N][N];

int dx[4] = {-1,0,1,0},dy[4] = {0,1,0,-1};

int n,st_x,st_y,ed_x,ed_y;


int bfs(){

    memset(d,0x3f3f3f3f,sizeof d);

    queue<PII> q;

    q.push({st_x,st_y});
    d[st_x][st_y] = 0;

    while(q.size()){

        auto t = q.front();

        q.pop();

        for(int i = 0;i < 4;i++){

            int x = t.first + dx[i],y = t.second + dy[i];

            if(x > 0 && x < n && y > 0 && y < n && g[x][y] == 0 && d[x][y] == 0x3f3f3f3f){

                d[x][y] = d[t.first][t.second] + 1;
                q.push({x,y});

                if(x == ed_x && y == ed_y)return d[x][y];
            }
        }
    }

    return -1;
}

int main(){


    scanf("%d%d%d%d%d%d",&n,&st_x,&st_y,&ed_x,&ed_y);

    for(int i = 1;i<=n;++i){
        for(int j = 1;j<=n;++j){
            scanf("%d",&g[i][j]);
        }
    }

    cout << bfs() << endl;

    system("pause");
    return 0;


}
