#include <iostream>
#include <cstring>

using namespace std;

const int N = 17;

int h[N], e[510], ne[510], w[510];
int dist[N], p[N];
bool st[N];
int n, idx, in, out;
int ans[N];
int cnt;
void add(int u, int v, int wi){
    e[idx] = v;
    ne[idx] = h[u];
    w[idx] = wi;
    h[u] = idx ++;
}
int dij(){
    dist[in] = 0;
    for(int i = 0;i<n;++i){
        int t = -1;
        for(int j = 0;j<n;++j){
            if(st[j])continue;
            if(t == -1 || dist[j] < dist[t]){
                t = j;
            }
        }
        st[t] = true;
        if(t == out)return 0;
        for(int i = h[t];~i;i = ne[i]){
            int v = e[i];
            if(!st[v]){
                if(dist[v] > dist[t] + w[i]){
                    dist[v] = dist[t] + w[i];
                    p[v] = t;
                }
            }
        }
    }
    return 0;
}
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);
    memset(h, -1, sizeof h);
    memset(dist, 0x3f, sizeof dist);
    cin >> n;
    for(int i = 0;i<n;++i){
        for(int j = 0;j<n;++j){
            int x;
            cin >> x;
            if(x){
                add(i, j, x);
            }
        }
    }

    for(int i = 0;i<n;++i){
        int available;
        cin >> available;
    }

    cin >> in >> out;
    p[in] = -1;
    dij();
    int pos = out;
    while(p[pos] > -1){
        ans[cnt] = pos;
        pos = p[pos];
        cnt ++;
    }
    for(int i = cnt;i >= 0;--i){
        cout << ans[i] << " ";
    }
    return 0;
}