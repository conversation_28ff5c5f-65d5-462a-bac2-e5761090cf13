#include<iostream>
#include<tuple>
#include<algorithm>
using namespace std;

const int N = 25;

int g[N][N];

int n,m,k,idx = 0;
int fx,fy,res;
typedef tuple<int,int,int> Tp;

Tp p[N*N];

bool cmp(Tp a,Tp b){
    return get<2>(a) > get<2>(b);
}

int main(){

    scanf("%d%d%d",&n,&m,&k);

    for(int i = 1;i<=n;++i){
        for(int j = 1;j<=m;++j){
            scanf("%d",&g[i][j]);

            if(g[i][j]){
                Tp a(i,j,g[i][j]);
                p[++idx] = a;
            }
        }
    }

    sort(p+1,p+1+idx,cmp);

    k--;  //跳入第一行
    fx = 1;
    fy = get<1>(p[1]);    
    for(int i = 1;i<=idx;++i){
        k--;  //假设要采摘花生
        int dis = abs(fx - get<0>(p[i])) + abs(fy - get<1>(p[i]));
        //dis代表了曼哈顿距离 或者说是前往时间
        k -= dis;  //剩余时间

        if(k>=get<0>(p[i])){
            //get<0>(p[i])是返程时间
            res += get<2>(p[i]);
            fx = get<0>(p[i]);
            fy = get<1>(p[i]);
        }else{
            cout << res << endl;
            return 0;
        }

        if(i == idx){
            cout << res << endl;
            return 0;
        }
        
    }
 
}