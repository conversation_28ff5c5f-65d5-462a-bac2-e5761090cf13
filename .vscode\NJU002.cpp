#include <iostream>
#include <cstring>
#include <string>
using namespace std;

const int N = 1e7 + 10;

int dp[N];
char s[N];
int n, ans, cnt;
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    while(cin >> n){
        cin >> (s + 1);
        ans = -1e9;
        cnt = 0;
        for(int i = 1;i<=n;++i){
            if(s[i] == '1'){
                cnt ++;
                dp[i] = max(-1, dp[i - 1] - 1);

            }else{
                dp[i] = max(1, dp[i - 1] + 1);
            }
            ans = max(ans, dp[i]);
        }

        cout << max(cnt, ans + cnt) << endl;
    }

    return 0;

}