#include <iostream>
#include <cstring>
#include <string>
#include <queue>
using namespace std;

typedef pair<int,int> PII;
const int N = 10;
int n, m, st, ed;
string s;
queue<PII> q;
char g[N][N];
int dist[N][N];
bool state[N][N];
// void bfs(){
//     memset(dist, 0x3f, sizeof dist);
//     dist[st][ed] = 0;
//     state[st][ed] = true;
//     q.push({st, ed});
//     while(q.size()){
//         auto t = q.front();
//         int r = t.first, c = t.second;
//         if(r >=0 && r < n && c >= 0 && c < )
//     }

// }
int main(){

    ios::sync_with_stdio(false);
    cin.tie(0);

    cin >> n;
    cin.ignore();
    for(int i = 0;i<n;++i){
        getline(cin, s);
        for(int j = 0;2*j<s.size();++j){
             g[i][j] = s[j * 2];
             if(g[i][j] == 2){
                 st = i, ed = j;
             }
         }
         m = strlen(g[i]);
    }

    for(int i = 0;i<n;++i){
        for(int j = 0;g[i][j];++j){
            cout << g[i][j];
        }
        cout << endl;
    }

    cout << n << " " << m << endl;

    return 0;
}