#include<iostream>
#include <chrono>

using namespace std;
	using namespace std::chrono;


const int N = 50;

	int fib[N]; //注意，fib存储的值有上限
int n;
	int dfs(int n){

    
    fib[1] = 1,fib[2] = 2,fib[3] = 4;

    if(n>3){
        for(int i = 4;i<=n;++i){
            fib[i] = fib[i-3]+fib[i-2]+fib[i-1];
        }
    }


   return fib[n];	}
	int main(){
    scanf("%d",&n);

   high_resolution_clock::time_point t1 = high_resolution_clock::now();

   cout << dfs(n) << endl;

	    high_resolution_clock::time_point t2 = high_resolution_clock::now();
 
   // 计算持续时间
    duration<double, std::milli> time_span = t2 - t1;
 
    // 输出运行时间（毫秒）
	    std::cout << "It took " << time_span.count() << " ms." << std::endl;
	system("pause");
    return 0;
	}
