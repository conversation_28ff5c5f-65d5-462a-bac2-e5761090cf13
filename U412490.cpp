#include<iostream>
#include<cstring>

typedef long long ll;
using namespace std;

const int N = 1e6 + 10;
const long long MOD = 1e10, MAX = 1e16;

int T, cnt, n;
ll f[N][4][4];
ll ans;

int main(){

    ios::sync_with_stdio(false); cin.tie(0); cout.tie(0); cin >> T;
    while(T--){
    cin >> n;
    memset(f, 0, sizeof f);
    for(int i = 1;i<=3;++i){
        f[1][i][0] = 1;
        for(int j = 1;j<=3;++j)f[2][i][j] = 1;
    }
    for(int i = 3;i<=n;++i){
        for(int j = 1;j<=3;++j){
            for(int k = 1;k<=3;++k){
                if(j == k){
                    for(int s = 1;s<=3;++s){
                        if(s != j){
                            f[i][s][j] += (f[i-1][j][k] % MAX);
                        }
                    }
                }else{
                    for(int s = 1;s<=3;++s){
                            f[i][s][j] += (f[i-1][j][k] % MAX);
                    }
                }
            }
        }
    }
        ans = 0;
        for(int i = 1;i<=3;++i){
            for(int j = 1;j<=3;++j){
                ans += (f[n][i][j] % MAX);
            }
        }

        if(n == 1){
            ans = 3;
        }

        if(ans >= MAX){
            cout << "......" << ans % MOD << endl;
        }else{
            cout << ans << endl;
        }
    }
    
    return 0;
}