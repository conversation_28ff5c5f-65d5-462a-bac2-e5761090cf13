#include<stdio.h>



//写一个转换字符的函数
char invert(char ch){

    int ascii = ch;  //把字符转化为其对应的ascii码
    
    /* A = 65, B = 66, ... Z = 90 */

/*
这里介绍一种技巧，我们把
[65,66,67,...,89,90]看成一个列表，然后列表长度是
90 - 65 + 1 = 26
我们想把65映射到87，66映射到88
可以使用 (65 - 65 - 4 + 26) % 26 + 65 = 87这个操作

所以我们利用这个技巧，总会把(i - 65 - 4 + 26) % 26 + 25 映射到j

  
*/
    int ascii_new = (ascii - 65 - 4 + 26) % 26 + 65;

    return ascii_new; //由于函数声明返回值是char，所以我们返回了一个整形数字，系统会自动将数字ASCII码对应的字符返还给调用者

}
int main()
{	
	for(int i = 0;i<5;++i){
        char ch;
        scanf("%c",&ch);  //输入

        printf("%c",invert(ch)); //输出
    }
	return 0;
}